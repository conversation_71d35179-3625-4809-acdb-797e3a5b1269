import { list } from "@keystone-6/core";
import { allOperations } from "@keystone-6/core/access";
import {
  json,
  relationship,
  select,
  text,
  timestamp,
} from "@keystone-6/core/fields";

import { isSignedIn, permissions, rules } from "../access";
import { trackingFields } from "./trackingFields";

export const Shop = list({
  access: {
    operation: {
      query: isSignedIn,
      create: permissions.canCreateShops,
      update: isSignedIn,
      delete: permissions.canManageShops,
    },
    filter: {
      query: rules.canReadShops,
      update: rules.canManageShops,
      delete: rules.canManageShops,
    },
  },
  ui: {
    listView: {
      initialColumns: ["name", "domain", "platform", "linkMode"],
    },
  },
  fields: {
    name: text({
      validation: { isRequired: true },
    }),
    domain: text(),
    accessToken: text({
      ui: {
        displayMode: "textarea",
      },
    }),
    linkMode: select({
      options: [
        { label: "Sequential", value: "sequential" },
        { label: "Simultaneous", value: "simultaneous" },
      ],
      defaultValue: "sequential",
    }),
    metadata: json({
      defaultValue: {},
    }),

    // Relationships
    platform: relationship({
      ref: "ShopPlatform.shops",
    }),
    user: relationship({
      ref: "User.shops",
      hooks: {
        resolveInput: ({ operation, resolvedData, context }) => {
          if (
            operation === "create" &&
            !resolvedData.user &&
            context.session?.itemId
          ) {
            return { connect: { id: context.session.itemId } };
          }
          return resolvedData.user;
        },
      },
    }),
    links: relationship({
      ref: "Link.shop",
      many: true,
    }),
    orders: relationship({
      ref: "Order.shop",
      many: true,
    }),
    shopItems: relationship({
      ref: "ShopItem.shop",
      many: true,
    }),

    ...trackingFields,
  },
});
