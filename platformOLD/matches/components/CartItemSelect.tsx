'use client';

import React, { useState, useEffect } from 'react';
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, Plus, Loader2, Search, AlertTriangle } from 'lucide-react';
import { searchChannelProducts } from '../actions/matches';
import { useToast } from '@/components/ui/use-toast';
import { Badge } from '@/components/ui/badge';

interface Channel {
  id: string;
  name: string;
  domain?: string;
  status?: string;
}

interface Product {
  image?: string;
  title: string;
  productId: string;
  variantId: string;
  price: string;
  availableForSale?: boolean;
  inventory?: number;
  inventoryTracked?: boolean;
}

interface SelectedCartItem {
  quantity: number;
  productId: string;
  variantId: string;
  price: string;
  channel: {
    id: string;
    name: string;
  };
  title?: string;
  image?: string;
}

interface CartItemSelectProps {
  orderId?: string;
  selectedItems: SelectedCartItem[];
  onSelectionChange: (items: SelectedCartItem[]) => void;
  channels: Channel[];
}

export const CartItemSelect: React.FC<CartItemSelectProps> = ({
  orderId,
  selectedItems,
  onSelectionChange,
  channels,
}) => {
  const [selectedChannelId, setSelectedChannelId] = useState(channels[0]?.id || '');
  const [searchEntry, setSearchEntry] = useState('');
  const [searchResults, setSearchResults] = useState<Product[]>([]);
  const [quantities, setQuantities] = useState<Record<string, number>>({});
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  // Update selected channel when channels change
  useEffect(() => {
    if (!selectedChannelId && channels.length > 0) {
      setSelectedChannelId(channels[0].id);
    }
  }, [channels, selectedChannelId]);

  const handleSearch = async () => {
    if (!selectedChannelId || !searchEntry) {
      toast({
        title: 'Missing Information',
        description: 'Please select a channel and enter a search term.',
        variant: 'destructive',
      });
      return;
    }

    setLoading(true);
    try {
      const response = await searchChannelProducts(selectedChannelId, searchEntry);
      
      if (response.error) {
        throw new Error(response.error);
      }
      
      setSearchResults(response.searchChannelProducts?.products || []);
    } catch (error: any) {
      toast({
        title: 'Search Failed',
        description: error.message,
        variant: 'destructive',
      });
      setSearchResults([]);
    } finally {
      setLoading(false);
    }
  };

  const getQuantity = (productId: string) => {
    const key = `${selectedChannelId}-${productId}`;
    return quantities[key] || 1;
  };

  const setQuantity = (productId: string, quantity: number) => {
    const key = `${selectedChannelId}-${productId}`;
    setQuantities(prev => ({ ...prev, [key]: Math.max(1, quantity) }));
  };

  const handleAddItem = (product: Product) => {
    const selectedChannel = channels.find(channel => channel.id === selectedChannelId);
    if (!selectedChannel) return;

    const quantity = getQuantity(product.productId);
    const newItem: SelectedCartItem = {
      quantity,
      productId: product.productId,
      variantId: product.variantId,
      price: product.price,
      channel: {
        id: selectedChannel.id,
        name: selectedChannel.name,
      },
      title: product.title,
      image: product.image,
    };

    onSelectionChange([...selectedItems, newItem]);
    
    // Reset quantity for this product
    setQuantity(product.productId, 1);
    
    toast({
      title: 'Item Added',
      description: `${product.title} added to selection`,
    });
  };

  const removeSelectedItem = (index: number) => {
    const newItems = [...selectedItems];
    newItems.splice(index, 1);
    onSelectionChange(newItems);
  };

  const calculateTotal = (item: SelectedCartItem) => {
    const total = parseFloat(item.price) * item.quantity;
    return total.toFixed(2);
  };

  const selectedChannel = channels.find(channel => channel.id === selectedChannelId);
  const isChannelInactive = selectedChannel?.status === 'inactive';

  return (
    <div className="flex flex-col h-full gap-4">
      {/* Search Controls */}
      <div className="flex gap-2">
        <Select value={selectedChannelId} onValueChange={setSelectedChannelId} disabled={channels.length === 0}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder={channels.length === 0 ? "No channels available" : "Select a channel"} />
          </SelectTrigger>
          <SelectContent>
            {channels.map((channel) => (
              <SelectItem key={channel.id} value={channel.id}>
                <div className="flex items-center gap-2">
                  {channel.name}
                  {channel.status === 'inactive' && (
                    <Badge variant="destructive" className="text-xs">
                      Inactive
                    </Badge>
                  )}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Input
          placeholder="Search channel products..."
          value={searchEntry}
          onChange={(e) => setSearchEntry(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
          className="flex-1"
          disabled={isChannelInactive}
        />
        <Button 
          onClick={handleSearch} 
          disabled={loading || !selectedChannelId || !searchEntry || isChannelInactive}
        >
          {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Search className="h-4 w-4" />}
        </Button>
      </div>

      {/* Channel Warning */}
      {isChannelInactive && (
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
          <div className="flex items-center gap-2 text-yellow-800 dark:text-yellow-200">
            <AlertTriangle className="h-4 w-4" />
            <span className="text-sm font-medium">Channel Unavailable</span>
          </div>
          <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
            This channel is currently inactive and cannot be used for product searches.
          </p>
        </div>
      )}

      {/* Selected Items */}
      {selectedItems.length > 0 && (
        <div className="border rounded-lg p-3 bg-green-50/30 dark:bg-green-900/10">
          <h4 className="text-sm font-medium mb-2">Selected Channel Products ({selectedItems.length})</h4>
          <div className="space-y-2 max-h-32 overflow-y-auto">
            {selectedItems.map((item, index) => (
              <div key={`${item.channel.id}-${item.productId}-${index}`} className="flex items-center justify-between text-sm bg-white dark:bg-gray-800 p-2 rounded">
                <div className="flex items-center gap-2">
                  {item.image && (
                    <img src={item.image} alt={item.title} className="w-8 h-8 rounded object-cover" />
                  )}
                  <div>
                    <div className="font-medium">{item.title}</div>
                    <div className="text-xs text-muted-foreground">
                      {item.channel.name} • Qty: {item.quantity} • ${calculateTotal(item)}
                    </div>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => removeSelectedItem(index)}
                  className="h-6 w-6 p-0"
                >
                  ×
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Search Results */}
      <div className="flex-1 overflow-y-auto">
        {searchResults.length > 0 ? (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Search Results ({searchResults.length})</h4>
            {searchResults.map((product) => (
              <div
                key={`${product.productId}-${product.variantId}`}
                className="flex items-center justify-between p-3 bg-background rounded-lg border"
              >
                <div className="flex items-center gap-3 flex-1">
                  {product.image && (
                    <div className="w-12 h-12 flex-shrink-0 rounded-md overflow-hidden">
                      <img
                        src={product.image}
                        alt={product.title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  )}
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium truncate">{product.title}</div>
                    <div className="text-xs text-muted-foreground truncate">
                      {product.productId} | {product.variantId}
                    </div>
                    <div className="text-sm font-medium text-green-600">
                      ${parseFloat(product.price).toFixed(2)}
                    </div>
                    {product.inventoryTracked && (
                      <div className="text-xs text-muted-foreground">
                        Stock: {product.inventory || 0}
                      </div>
                    )}
                    {!product.availableForSale && (
                      <Badge variant="destructive" className="text-xs mt-1">
                        Out of Stock
                      </Badge>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center gap-2 flex-shrink-0">
                  <div className="flex items-center gap-1">
                    <Button
                      variant="outline"
                      size="icon"
                      className="h-6 w-6"
                      onClick={() => setQuantity(product.productId, getQuantity(product.productId) - 1)}
                      disabled={getQuantity(product.productId) <= 1}
                    >
                      <ChevronLeft className="h-3 w-3" />
                    </Button>
                    <Input
                      className="w-12 h-6 text-center text-xs"
                      type="number"
                      min="1"
                      value={getQuantity(product.productId)}
                      onChange={(e) => setQuantity(product.productId, parseInt(e.target.value) || 1)}
                    />
                    <Button
                      variant="outline"
                      size="icon"
                      className="h-6 w-6"
                      onClick={() => setQuantity(product.productId, getQuantity(product.productId) + 1)}
                    >
                      <ChevronRight className="h-3 w-3" />
                    </Button>
                  </div>
                  
                  <div className="text-xs text-muted-foreground min-w-[60px] text-right">
                    ${(parseFloat(product.price) * getQuantity(product.productId)).toFixed(2)}
                  </div>
                  
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-6 w-6"
                    onClick={() => handleAddItem(product)}
                    disabled={!product.availableForSale}
                  >
                    <Plus className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        ) : searchEntry && !loading && !isChannelInactive ? (
          <div className="text-center text-muted-foreground py-8">
            No products found for "{searchEntry}"
          </div>
        ) : (
          <div className="text-center text-muted-foreground py-8">
            {isChannelInactive 
              ? 'Please select an active channel to search for products'
              : selectedChannelId 
                ? 'Enter a search term to find products' 
                : 'Select a channel to start searching'
            }
          </div>
        )}
      </div>
    </div>
  );
};