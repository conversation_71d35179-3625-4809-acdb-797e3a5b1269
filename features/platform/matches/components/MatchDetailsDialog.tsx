'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { LineItemSelect } from './LineItemSelect';
import { CartItemSelect } from './CartItemSelect';
import { createMatch } from '../actions/matches';
import { useToast } from '@/components/ui/use-toast';
import { Loader2 } from 'lucide-react';

interface MatchDetailsDialogProps {
  open: boolean;
  onClose: () => void;
  orderId?: string;
  shops?: any[];
  channels?: any[];
}

interface SelectedLineItem {
  quantity: number;
  productId: string;
  variantId: string;
  shop: {
    id: string;
    name: string;
  };
  price?: string;
  title?: string;
  image?: string;
}

interface SelectedCartItem {
  quantity: number;
  productId: string;
  variantId: string;
  price: string;
  channel: {
    id: string;
    name: string;
  };
  title?: string;
  image?: string;
}

export const MatchDetailsDialog: React.FC<MatchDetailsDialogProps> = ({
  open,
  onClose,
  orderId,
  shops = [],
  channels = [],
}) => {
  const [selectedLineItems, setSelectedLineItems] = useState<SelectedLineItem[]>([]);
  const [selectedCartItems, setSelectedCartItems] = useState<SelectedCartItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('input');
  const { toast } = useToast();

  const handleCreateMatch = async () => {
    if (selectedLineItems.length === 0 || selectedCartItems.length === 0) {
      toast({
        title: 'Incomplete Selection',
        description: 'Please select both line items and cart items to create a match.',
        variant: 'destructive',
      });
      return;
    }

    setLoading(true);
    try {
      // Transform selected items into the format expected by createMatch
      const shopItemIds = selectedLineItems.map(item => ({ id: item.productId })); // This would need proper shop item creation
      const channelItemIds = selectedCartItems.map(item => ({ id: item.productId })); // This would need proper channel item creation
      
      const matchData = {
        input: { connect: shopItemIds },
        output: { connect: channelItemIds },
      };
      
      const response = await createMatch(matchData);
      
      if (response.error) {
        throw new Error(response.error);
      }

      toast({
        title: 'Success',
        description: 'Match created successfully!',
      });
      
      // Reset state and close dialog
      setSelectedLineItems([]);
      setSelectedCartItems([]);
      onClose();
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setSelectedLineItems([]);
      setSelectedCartItems([]);
      onClose();
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>Create Match</DialogTitle>
        </DialogHeader>
        
        <div className="flex-1 flex flex-col overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="input" className="flex items-center gap-2">
                Shop Products
                {selectedLineItems.length > 0 && (
                  <span className="bg-blue-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[16px] h-4 flex items-center justify-center">
                    {selectedLineItems.length}
                  </span>
                )}
              </TabsTrigger>
              <TabsTrigger value="output" className="flex items-center gap-2">
                Channel Products
                {selectedCartItems.length > 0 && (
                  <span className="bg-green-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[16px] h-4 flex items-center justify-center">
                    {selectedCartItems.length}
                  </span>
                )}
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="input" className="flex-1 overflow-hidden">
              <LineItemSelect
                orderId={orderId}
                selectedItems={selectedLineItems}
                onSelectionChange={setSelectedLineItems}
                shops={shops}
              />
            </TabsContent>
            
            <TabsContent value="output" className="flex-1 overflow-hidden">
              <CartItemSelect
                orderId={orderId}
                selectedItems={selectedCartItems}
                onSelectionChange={setSelectedCartItems}
                channels={channels}
              />
            </TabsContent>
          </Tabs>
        </div>
        
        <div className="flex justify-between items-center pt-4 border-t">
          <div className="text-sm text-muted-foreground">
            Selected: {selectedLineItems.length} shop products, {selectedCartItems.length} channel products
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleClose} disabled={loading}>
              Cancel
            </Button>
            <Button 
              onClick={handleCreateMatch} 
              disabled={loading || selectedLineItems.length === 0 || selectedCartItems.length === 0}
            >
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Create Match
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};