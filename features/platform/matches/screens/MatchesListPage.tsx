import { getListByPath } from "@/features/dashboard/actions";
import { getFilteredMatches } from "@/features/platform/matches/actions/matches";
import { getFilteredShops } from "@/features/platform/shops/actions";
import { getFilteredChannels } from "@/features/platform/channels/actions";
import { MatchesListClient } from './MatchesListClient';

interface PageProps {
  searchParams: { [key: string]: string | string[] | undefined };
}

function ErrorDisplay({ title, message }: { title: string; message: string }) {
  return (
    <div className="px-4 sm:px-6 lg:px-8 py-8">
      <h1 className="text-2xl font-bold tracking-tight text-red-600">
        {title}
      </h1>
      <p className="mt-2 text-gray-600">{message}</p>
    </div>
  );
}

export default async function MatchesListPage({ searchParams }: PageProps) {
  const page = Number(searchParams.page) || 1;
  const pageSize = Number(searchParams.pageSize) || 10;

  let status = null;
  const statusFilter = searchParams["!status_matches"];
  if (statusFilter) {
    try {
      const parsed = JSON.parse(decodeURIComponent(statusFilter as string));
      if (Array.isArray(parsed) && parsed.length > 0) {
        status = parsed[0].value;
      }
    } catch (e) {
      // Invalid JSON in URL, ignore
    }
  }

  const search = typeof searchParams.search === "string" && searchParams.search !== "" ? searchParams.search : null;

  const [list, matchesData, shopsData, channelsData] = await Promise.all([
    getListByPath("matches"),
    getFilteredMatches(
      search,
      page,
      pageSize,
      (() => {
        const sortBy = searchParams.sortBy as string | undefined;
        return sortBy ? {
          field: sortBy.startsWith("-") ? sortBy.slice(1) : sortBy,
          direction: sortBy.startsWith("-") ? "DESC" : "ASC"
        } : null;
      })()
    ),
    getFilteredShops(null, 1, 100),
    getFilteredChannels(null, 1, 100),
  ]);

  if (!list) {
    return (
      <ErrorDisplay
        title="Invalid List"
        message="The requested list could not be found."
      />
    );
  }

  const matches = matchesData.success ? matchesData.data?.items || [] : [];
  const count = matchesData.success ? matchesData.data?.count || 0 : 0;
  const shops = shopsData.success ? shopsData.data?.items || [] : [];
  const channels = channelsData.success ? channelsData.data?.items || [] : [];

  // Create status counts for matches
  const statusCounts = {
    shop: shops.length,
    channel: channels.length,
    matches: count,
  };

  return (
    <MatchesListClient
      matches={matches}
      count={count}
      list={list}
      statusCounts={statusCounts}
      shops={shops}
      channels={channels}
      searchParams={searchParams}
    />
  );
}