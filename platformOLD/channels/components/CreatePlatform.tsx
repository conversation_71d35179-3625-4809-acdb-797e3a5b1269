"use client";
import React, { useMemo, useState } from "react";
import { useCreateItem } from "@/features/dashboard/hooks/useCreateItem";
import { useAdminMeta } from "@/features/dashboard/hooks/useAdminMeta";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Fields } from "@/features/dashboard/components/Fields";
import { GraphQLErrorNotice } from "@/features/dashboard/components/GraphQLErrorNotice";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { getFilteredProps } from "../../shops/components/CreatePlatform";

// TODO: Import from actual channel adapters when available
const channelAdapters = {
  shopify: "shopify",
  bigcommerce: "bigcommerce",
  woocommerce: "woocommerce",
  Medusa: "soon",
  Ma<PERSON><PERSON>: "soon",
  Stripe: "soon",
};

export function CreatePlatform({ trigger }: { trigger: React.ReactNode }) {
  const [selectedPlatform, setSelectedPlatform] = useState<string | undefined>(undefined);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const { adminMeta } = useAdminMeta();
  const list = adminMeta.lists.ChannelPlatform;
  const { create, props, state, error } = useCreateItem(list);

  const keysToUpdateCustom = [
    "name",
    "createPurchaseFunction",
    "getWebhooksFunction",
    "deleteWebhookFunction",
    "createWebhookFunction",
    "searchProductsFunction",
    "getProductFunction",
    "cancelPurchaseWebhookHandler",
    "createTrackingWebhookHandler",
    "oAuthFunction",
    "oAuthCallbackFunction",
    "appKey",
    "appSecret",
  ];

  const keysToUpdateTemplate = ["name", "appKey", "appSecret"];

  const handlePlatformActivation = async () => {
    const item = await create();
    if (item) {
      // Server action already handles revalidation
      clearFunctionFields();
      setIsDialogOpen(false);
    }
  };

  const handleTemplateSelection = (value: string) => {
    setSelectedPlatform(value);

    if (value === "custom") {
      clearFunctionFields();
    } else {
      props.onChange((oldValues: any) => {
        const newValues = { ...oldValues };
        keysToUpdateCustom
          .filter((key) => !["appKey", "appSecret"].includes(key))
          .forEach((key) => {
            newValues[key] = {
              ...oldValues[key],
              value: {
                ...oldValues[key].value,
                inner: {
                  ...oldValues[key].value.inner,
                  value:
                    key === "name"
                      ? value.charAt(0).toUpperCase() + value.slice(1)
                      : value,
                },
              },
            };
          });
        return newValues;
      });
    }
  };

  const clearFunctionFields = () => {
    const clearedFields = keysToUpdateCustom.reduce((acc: any, key) => {
      acc[key] = {
        ...props.value[key],
        value: {
          ...props.value[key].value,
          inner: {
            ...props.value[key].value.inner,
            value: "",
          },
        },
      };
      return acc;
    }, {});

    props.onChange((prev: any) => ({ ...prev, ...clearedFields }));
  };

  const handleDialogClose = () => {
    setSelectedPlatform(undefined);
    clearFunctionFields();
    setIsDialogOpen(false);
  };

  const filteredProps = useMemo(() => {
    const fieldKeysToShow =
      selectedPlatform === "custom" ? keysToUpdateCustom : keysToUpdateTemplate;

    const modifications = fieldKeysToShow.map((key) => ({ key }));

    return getFilteredProps(props, [...modifications], true);
  }, [props, selectedPlatform]);

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>

      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create Channel Platform</DialogTitle>
          <DialogDescription>
            {selectedPlatform === "custom"
              ? "Create a custom platform from scratch by providing the necessary fields"
              : "Create a platform based on an existing template"}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-2">
          <Label className="text-base pb-2">Platform</Label>
          <Select
            onValueChange={handleTemplateSelection}
            value={selectedPlatform}
          >
            <SelectTrigger className="w-full bg-muted/40 text-base">
              <SelectValue placeholder="Select a platform" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel className="-ml-6">Templates</SelectLabel>
                {Object.keys(channelAdapters).map((key) => (
                  <SelectItem
                    key={key}
                    value={key}
                    disabled={channelAdapters[key as keyof typeof channelAdapters] === "soon"}
                  >
                    {key.charAt(0).toUpperCase() + key.slice(1)}
                  </SelectItem>
                ))}
              </SelectGroup>
              <SelectGroup>
                <SelectLabel className="-ml-6">Custom</SelectLabel>
                <SelectItem value="custom">Start from scratch...</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>

        {selectedPlatform && (
          <div className="bg-muted/20 p-4 border rounded-lg overflow-auto max-h-[50vh]">
            {error && (
              <GraphQLErrorNotice
                networkError={error?.networkError}
                errors={error?.graphQLErrors}
              />
            )}
            <Fields {...filteredProps} />
          </div>
        )}

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleDialogClose}
          >
            Cancel
          </Button>
          <Button
            disabled={!selectedPlatform || state === "loading"}
            onClick={handlePlatformActivation}
          >
            {state === "loading" ? "Creating..." : "Create Platform"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}