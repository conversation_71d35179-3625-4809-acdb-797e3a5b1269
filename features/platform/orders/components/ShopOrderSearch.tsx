"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import {
  Search,
  Store,
  Plus,
  ChevronDown,
  ChevronRight,
  ArrowLeft,
  ArrowRight,
} from "lucide-react";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { getShops } from "@/features/platform/shops/actions/shops";
import { searchShopOrders } from "@/features/platform/shops/actions/search-orders";
import { createOrderFromShopOrder } from "../actions/create-from-shop-order";
import type { ShopOrderData } from "../actions/create-from-shop-order";

interface Shop {
  id: string;
  name: string;
  domain: string;
  platform?: {
    id: string;
    name: string;
  };
}

export function ShopOrderSearch() {
  const [shops, setShops] = useState<Shop[]>([]);
  const [selectedShopId, setSelectedShopId] = useState<string>("");
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [orders, setOrders] = useState<any[]>([]);
  const [hasNextPage, setHasNextPage] = useState(false);
  const [skip, setSkip] = useState(0);
  const [after, setAfter] = useState(null);
  const [error, setError] = useState<string | null>(null);
  const pageSize = 10;

  // Load shops on mount
  useEffect(() => {
    loadShops();
  }, []);

  const loadShops = async () => {
    try {
      const response = await getShops();
      if (response.success) {
        setShops(response.data?.items || []);
      }
    } catch (error) {
      console.error("Error loading shops:", error);
    }
  };

  const handleSearch = async () => {
    if (!selectedShopId) return;
    
    setIsLoading(true);
    setError(null);
    setSkip(0);
    setAfter(null);
    
    try {
      const response = await searchShopOrders(selectedShopId, searchTerm, pageSize, 0, null);
      
      if (response.success) {
        const searchResults = response.data?.searchShopOrders;
        setOrders(searchResults?.orders || []);
        setHasNextPage(searchResults?.hasNextPage || false);
      } else {
        setError(response.error || "Failed to search orders");
        setOrders([]);
      }
    } catch (err: any) {
      setError(err.message);
      setOrders([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleNextPage = async () => {
    if (!selectedShopId || !hasNextPage) return;
    
    const newSkip = skip + pageSize;
    setIsLoading(true);
    
    try {
      const response = await searchShopOrders(selectedShopId, searchTerm, pageSize, newSkip, after);
      
      if (response.success) {
        const searchResults = response.data?.searchShopOrders;
        setOrders(searchResults?.orders || []);
        setHasNextPage(searchResults?.hasNextPage || false);
        setSkip(newSkip);
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePreviousPage = async () => {
    if (!selectedShopId || skip === 0) return;
    
    const newSkip = Math.max(0, skip - pageSize);
    setIsLoading(true);
    
    try {
      const response = await searchShopOrders(selectedShopId, searchTerm, pageSize, newSkip, null);
      
      if (response.success) {
        const searchResults = response.data?.searchShopOrders;
        setOrders(searchResults?.orders || []);
        setHasNextPage(searchResults?.hasNextPage || false);
        setSkip(newSkip);
        setAfter(null);
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateOrder = async (order: any) => {
    try {
      const shopOrderData: ShopOrderData = {
        orderId: order.orderId,
        orderName: order.orderName,
        email: order.email,
        firstName: order.firstName,
        lastName: order.lastName,
        streetAddress1: order.streetAddress1,
        streetAddress2: order.streetAddress2,
        city: order.city,
        state: order.state,
        zip: order.zip,
        country: order.country,
        phone: order.phone,
        currency: order.currency,
        totalPrice: order.totalPrice,
        subTotalPrice: order.subTotalPrice,
        totalDiscounts: order.totalDiscounts,
        totalTax: order.totalTax,
        date: order.date,
        lineItems: order.lineItems,
        cartItems: order.cartItems,
        shop: { id: selectedShopId }
      };
      
      const result = await createOrderFromShopOrder(shopOrderData);
      if (result.success) {
        // Show success notification
        console.log("Order created successfully:", result.data);
        // Optionally redirect to the order page
        // window.location.href = `/dashboard/platform/orders/${result.data.id}`;
      } else {
        setError(result.error || "Failed to create order");
      }
    } catch (error) {
      console.error("Error creating order:", error);
      setError("Failed to create order");
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Store className="h-5 w-5" />
          Search Shop Orders
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search Controls */}
        <div className="flex gap-2">
          <div className="flex-1">
            <Select value={selectedShopId} onValueChange={setSelectedShopId}>
              <SelectTrigger>
                <SelectValue placeholder="Select a shop" />
              </SelectTrigger>
              <SelectContent>
                {shops.map((shop) => (
                  <SelectItem key={shop.id} value={shop.id}>
                    <div className="flex items-center gap-2">
                      <span>{shop.name}</span>
                      {shop.platform && (
                        <Badge variant="outline" className="text-xs">
                          {shop.platform.name}
                        </Badge>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex-1">
            <Input
              placeholder="Search orders..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleSearch();
                }
              }}
            />
          </div>
          <Button onClick={handleSearch} disabled={!selectedShopId || isLoading}>
            <Search className="h-4 w-4 mr-2" />
            Search
          </Button>
        </div>

        {/* Pagination Controls */}
        {orders.length > 0 && (
          <div className="flex items-center justify-between">
            <div className="flex gap-1">
              <Button
                variant="outline"
                size="sm"
                onClick={handlePreviousPage}
                disabled={skip === 0 || isLoading}
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleNextPage}
                disabled={!hasNextPage || isLoading}
              >
                <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
            <span className="text-sm text-muted-foreground">
              Showing {skip + 1}-{skip + orders.length} orders
            </span>
          </div>
        )}

        {/* Results */}
        {isLoading && <Skeleton className="h-[200px] w-full" />}
        
        {error && (
          <div className="p-4 border border-red-200 bg-red-50 dark:bg-red-900/20 dark:border-red-800 rounded-lg">
            <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
          </div>
        )}

        {orders.length > 0 && !isLoading && (
          <div className="space-y-2">
            {orders.map((order) => (
              <ShopOrderCard 
                key={order.orderId} 
                order={order} 
                onCreateOrder={() => handleCreateOrder(order)}
              />
            ))}
          </div>
        )}

        {!isLoading && !error && orders.length === 0 && selectedShopId && (
          <div className="text-center p-8 text-muted-foreground">
            <p>No orders found. Try adjusting your search criteria.</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

interface ShopOrderCardProps {
  order: any;
  onCreateOrder: () => void;
}

function ShopOrderCard({ order, onCreateOrder }: ShopOrderCardProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);

  const handleCreateOrder = async () => {
    setIsCreating(true);
    try {
      await onCreateOrder();
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <div className="border rounded-lg p-4">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <a
              href={order.link}
              target="_blank"
              rel="noopener noreferrer"
              className="font-medium text-sm hover:text-blue-600"
            >
              {order.orderName}
            </a>
            <span className="text-xs text-muted-foreground">
              {order.date}
            </span>
          </div>
          <div className="text-sm text-muted-foreground">
            <p>{order.firstName} {order.lastName}</p>
            <p>{order.streetAddress1}</p>
            {order.streetAddress2 && <p>{order.streetAddress2}</p>}
            <p>{order.city}, {order.state} {order.zip}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button
            size="sm"
            onClick={handleCreateOrder}
            disabled={isCreating}
          >
            <Plus className="h-4 w-4 mr-1" />
            {isCreating ? "Creating..." : "Create Order"}
          </Button>
          <Collapsible open={isOpen} onOpenChange={setIsOpen}>
            <CollapsibleTrigger asChild>
              <Button variant="outline" size="sm">
                {isOpen ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
              </Button>
            </CollapsibleTrigger>
          </Collapsible>
        </div>
      </div>
      
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleContent className="mt-4 pt-4 border-t">
          {order.lineItems && order.lineItems.length > 0 && (
            <div className="space-y-2">
              <h4 className="font-medium text-sm">Line Items</h4>
              {order.lineItems.map((item: any, index: number) => (
                <div key={index} className="flex items-center gap-3 p-2 bg-muted rounded">
                  {item.image && (
                    <img src={item.image} alt={item.name} className="w-10 h-10 object-cover rounded" />
                  )}
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">{item.name}</p>
                    <p className="text-xs text-muted-foreground">
                      {item.productId} | {item.variantId}
                    </p>
                    <p className="text-sm">
                      ${parseFloat(item.price).toFixed(2)} x {item.quantity}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
          
          {order.cartItems && order.cartItems.length > 0 && (
            <div className="space-y-2 mt-4">
              <h4 className="font-medium text-sm">Cart Items</h4>
              {order.cartItems.map((item: any, index: number) => (
                <div key={index} className="flex items-center gap-3 p-2 bg-green-50 dark:bg-green-900/20 rounded">
                  {item.image && (
                    <img src={item.image} alt={item.name} className="w-10 h-10 object-cover rounded" />
                  )}
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">{item.name}</p>
                    <p className="text-xs text-muted-foreground">
                      {item.productId} | {item.variantId}
                    </p>
                    <p className="text-sm">
                      ${parseFloat(item.price).toFixed(2)} x {item.quantity}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
}