import { createChannelWebhook as executeCreateChannelWebhook } from "../../utils/channelProviderAdapter";

interface CreateChannelWebhookArgs {
  channelId: string;
  topic: string;
  endpoint: string;
}

async function createChannelWebhook(
  root: any,
  { channelId, topic, endpoint }: CreateChannelWebhookArgs,
  context: any
) {
  try {
    // Fetch the channel using the provided channelId
    const channel = await context.query.Channel.findOne({
      where: { id: channelId },
      query: "id domain accessToken platform { id createWebhookFunction }",
    });

    if (!channel) {
      return { success: false, error: "Channel not found" };
    }

    if (!channel.platform) {
      return { success: false, error: "Platform configuration not specified." };
    }

    const result = await executeCreateChannelWebhook({
      platform: channel.platform,
      endpoint,
      events: [topic],
    });

    return { success: true, webhookId: result.webhookId };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
}

export default createChannelWebhook;