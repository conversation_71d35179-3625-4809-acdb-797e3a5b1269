"use client";

import React, { useState, useMemo } from "react";
import { useCreateItem } from "@/features/dashboard/hooks/useCreateItem";
import { useAdminMeta } from "@/features/dashboard/hooks/useAdminMeta";
import { Button } from "@/components/ui/button";
import { Fields } from "@/features/dashboard/components/Fields";
import { GraphQLErrorNotice } from "@/features/dashboard/components/GraphQLErrorNotice";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Package, User, MapPin, FileText, ShoppingCart, Settings } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";

export function getFilteredProps(props: any, modifications: any[], defaultCollapse?: boolean) {
  const fieldKeysToShow = modifications.map((mod) => mod.key);
  const breakGroups = modifications.reduce((acc: string[], mod) => {
    if (mod.breakGroup) {
      acc.push(mod.breakGroup);
    }
    return acc;
  }, []);

  const newFieldModes = { ...props.fieldModes };

  Object.keys(props.fields).forEach((key) => {
    if (!fieldKeysToShow.includes(key)) {
      newFieldModes[key] = "hidden";
    } else {
      newFieldModes[key] = props.fieldModes[key] || "edit";
    }
  });

  const updatedFields = Object.keys(props.fields).reduce((obj: any, key) => {
    const modification = modifications.find((mod) => mod.key === key);
    if (modification) {
      obj[key] = {
        ...props.fields[key],
        fieldMeta: {
          ...props.fields[key].fieldMeta,
          ...modification.fieldMeta,
        },
      };
    } else {
      obj[key] = props.fields[key];
    }
    return obj;
  }, {});

  const reorderedFields = modifications.reduce((obj: any, mod) => {
    obj[mod.key] = updatedFields[mod.key];
    return obj;
  }, {});

  const updatedGroups = props.groups.map((group: any) => {
    if (breakGroups.includes(group.label)) {
      return {
        ...group,
        fields: group.fields.filter(
          (field: any) => !fieldKeysToShow.includes(field.path)
        ),
      };
    }
    return {
      ...group,
      collapsed: defaultCollapse,
    };
  });

  return {
    ...props,
    fields: reorderedFields,
    fieldModes: newFieldModes,
    groups: updatedGroups,
  };
}

interface ProcessingOptions {
  linkOrder: boolean;
  matchOrder: boolean;
  processOrder: boolean;
}

export function CreateOrderFromScratch() {
  const { adminMeta } = useAdminMeta();
  const list = adminMeta.lists.Order;
  const { create, props, state, error } = useCreateItem(list);
  const { toast } = useToast();
  const router = useRouter();

  const [processingOptions, setProcessingOptions] = useState<ProcessingOptions>({
    linkOrder: false,
    matchOrder: false,
    processOrder: false,
  });

  const [activeTab, setActiveTab] = useState("order-details");

  const handleCreateOrder = async () => {
    try {
      const order = await create();
      if (order) {
        toast({
          title: "Success",
          description: "Order created successfully",
        });
        router.push(`/dashboard/platform/orders/${order.id}`);
      }
    } catch (err) {
      toast({
        title: "Error",
        description: "Failed to create order",
        variant: "destructive",
      });
    }
  };

  // Section configurations for different form parts
  const orderDetailsProps = useMemo(() => {
    const modifications = [
      { key: "orderName" },
      { key: "orderId" },
      { key: "shop" },
      { key: "status" },
      { key: "currency" },
      { key: "totalPrice" },
      { key: "subTotalPrice" },
      { key: "totalTax" },
      { key: "totalShipping" },
    ];
    return getFilteredProps(props, modifications);
  }, [props]);

  const customerInfoProps = useMemo(() => {
    const modifications = [
      { key: "email" },
      { key: "first_name" },
      { key: "last_name" },
      { key: "phone" },
    ];
    return getFilteredProps(props, modifications);
  }, [props]);

  const shippingAddressProps = useMemo(() => {
    const modifications = [
      { key: "streetAddress1" },
      { key: "streetAddress2" },
      { key: "city" },
      { key: "state" },
      { key: "zip" },
      { key: "country" },
    ];
    return getFilteredProps(props, modifications);
  }, [props]);

  const additionalInfoProps = useMemo(() => {
    const modifications = [
      { key: "note" },
      { key: "error" },
      { key: "user" },
    ];
    return getFilteredProps(props, modifications);
  }, [props]);

  const tabs = [
    {
      id: "order-details",
      label: "Order Details",
      icon: Package,
      description: "Basic order information and pricing",
      props: orderDetailsProps,
    },
    {
      id: "customer-info",
      label: "Customer Info",
      icon: User,
      description: "Customer contact details",
      props: customerInfoProps,
    },
    {
      id: "shipping-address",
      label: "Shipping Address",
      icon: MapPin,
      description: "Delivery location information",
      props: shippingAddressProps,
    },
    {
      id: "additional-info",
      label: "Additional Info",
      icon: FileText,
      description: "Notes and other details",
      props: additionalInfoProps,
    },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-semibold">Create Order from Scratch</h1>
        <p className="text-muted-foreground">
          Create a new order by filling in the details below.
        </p>
      </div>

      {error && (
        <GraphQLErrorNotice
          networkError={error?.networkError}
          errors={error?.graphQLErrors}
        />
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <TabsTrigger key={tab.id} value={tab.id} className="flex items-center gap-2">
                <Icon className="h-4 w-4" />
                <span className="hidden sm:inline">{tab.label}</span>
              </TabsTrigger>
            );
          })}
        </TabsList>

        {tabs.map((tab) => (
          <TabsContent key={tab.id} value={tab.id}>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <tab.icon className="h-5 w-5" />
                  {tab.label}
                </CardTitle>
                <CardDescription>{tab.description}</CardDescription>
              </CardHeader>
              <CardContent>
                {tab.props.fields && Object.keys(tab.props.fields).length > 0 ? (
                  <Fields {...tab.props} />
                ) : (
                  <p className="text-muted-foreground">No fields available for this section.</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        ))}
      </Tabs>

      <Separator />

      {/* Processing Options */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Processing Options
          </CardTitle>
          <CardDescription>
            Choose how the order should be processed after creation
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="linkOrder"
              checked={processingOptions.linkOrder}
              onChange={(e) =>
                setProcessingOptions(prev => ({ ...prev, linkOrder: e.target.checked }))
              }
              className="rounded border-gray-300"
            />
            <label htmlFor="linkOrder" className="text-sm font-medium">
              Link Order
            </label>
            <span className="text-xs text-muted-foreground">
              Automatically link order to channels based on shop configuration
            </span>
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="matchOrder"
              checked={processingOptions.matchOrder}
              onChange={(e) =>
                setProcessingOptions(prev => ({ ...prev, matchOrder: e.target.checked }))
              }
              className="rounded border-gray-300"
            />
            <label htmlFor="matchOrder" className="text-sm font-medium">
              Match Order
            </label>
            <span className="text-xs text-muted-foreground">
              Use matching system to find corresponding products on channels
            </span>
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="processOrder"
              checked={processingOptions.processOrder}
              onChange={(e) =>
                setProcessingOptions(prev => ({ ...prev, processOrder: e.target.checked }))
              }
              className="rounded border-gray-300"
            />
            <label htmlFor="processOrder" className="text-sm font-medium">
              Process Order
            </label>
            <span className="text-xs text-muted-foreground">
              Immediately process the order after creation
            </span>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-end gap-3">
        <Button
          variant="outline"
          onClick={() => router.back()}
        >
          Cancel
        </Button>
        <Button
          onClick={handleCreateOrder}
          disabled={state === "loading"}
        >
          {state === "loading" ? "Creating..." : "Create Order"}
        </Button>
      </div>
    </div>
  );
}