{"permissions": {"allow": ["Bash(rg:*)", "Bash(/Users/<USER>/.nvm/versions/node/v20.9.0/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -o \"from [''\"\"]([^''\"\"]+)[''\"\"]|require\\([''\"\"]([^''\"\"]+)[''\"\"]\\)\" -r '$1$2' -g '*.ts' -g '*.tsx' -g '*.js' -g '*.jsx')", "Bash(find:*)", "Bash(grep:*)", "Bash(npm install:*)", "Bash(npm run generate-views-index:*)", "Bash(ls:*)", "Bash(node:*)", "Bash(rm:*)", "WebFetch(domain:keystonejs.com)", "Bash(npm run dev:*)", "Bash(npx tsx:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(npm run build:*)", "<PERSON><PERSON>(mkdir:*)", "mcp__mcp-midscene__midscene_playwright_example", "mcp__playwright__browser_generate_playwright_test", "<PERSON><PERSON>(mv:*)", "mcp__ide__getDiagnostics"], "deny": []}}