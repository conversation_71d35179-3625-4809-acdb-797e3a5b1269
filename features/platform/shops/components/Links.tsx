"use client";

import React, { useState, useMemo, useEffect } from "react";
import {
  ArrowRight,
  Edit,
  Edit2,
  ListFilter,
  Plus,
  Trash2,
  X,
  ChevronDown,
  Pencil,
  CircleAlert,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { cn } from "@/lib/utils";
import { 
  getChannels, 
  getShopLinks, 
  createShopLink, 
  updateShopLink, 
  deleteShopLink 
} from "../actions/links";

export const CreateLinkButton = ({ shopId, refetch }: {
  shopId: string;
  refetch: () => void;
}) => {
  const [channels, setChannels] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const { toast } = useToast();

  const loadChannels = async () => {
    try {
      setLoading(true);
      const response = await getChannels();
      console.log('FINAL RESPONSE IN COMPONENT:', JSON.stringify(response, null, 2));
      if (response.success) {
        const channels = response.data?.channels || [];
        console.log('CHANNELS TO SET:', JSON.stringify(channels, null, 2));
        setChannels(channels);
      } else {
        console.log('RESPONSE ERROR:', response.error);
        setError(response.error || "Failed to load channels");
      }
    } catch (err: any) {
      console.error('COMPONENT ERROR:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadChannels();
  }, []);

  const handleCreateLink = async (channelId: string) => {
    setIsCreating(true);
    try {
      const response = await createShopLink(shopId, channelId);
      
      if (response.success) {
        toast({
          title: "Link created successfully",
        });
        refetch();
      } else {
        toast({
          title: "Failed to create link",
          description: response.error || "Unknown error",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      toast({
        title: "Failed to create link",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  if (loading) {
    return <Button disabled>Loading...</Button>;
  }

  if (error) {
    return (
      <div className="rounded-md border border-red-500/50 px-4 py-3 text-red-600">
        <p className="text-sm">
          <CircleAlert
            className="me-3 -mt-0.5 inline-flex opacity-60"
            size={16}
            aria-hidden="true"
          />
          Error loading channels: {error}
        </p>
      </div>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="gap-2" disabled={isCreating}>
          <Plus className="h-4 w-4" />
          {isCreating ? "Creating..." : "Create Link"}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>Select Channel</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          {channels.length === 0 ? (
            <DropdownMenuItem disabled>
              No channels available
            </DropdownMenuItem>
          ) : (
            channels.map((channel: any) => (
              <DropdownMenuItem
                key={channel.id}
                onClick={() => handleCreateLink(channel.id)}
              >
                {channel.name}
              </DropdownMenuItem>
            ))
          )}
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export const Links = ({ shopId }: { shopId: string }) => {
  const [links, setLinks] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadLinks = async () => {
    try {
      setLoading(true);
      const response = await getShopLinks(shopId);
      if (response.success) {
        setLinks(response.data?.links || []);
      } else {
        setError(response.error || "Failed to load links");
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadLinks();
  }, [shopId]);

  if (loading) {
    return <div>Loading links...</div>;
  }

  if (error) {
    return (
      <div className="rounded-md border border-red-500/50 px-4 py-3 text-red-600">
        <p className="text-sm">
          <CircleAlert
            className="me-3 -mt-0.5 inline-flex opacity-60"
            size={16}
            aria-hidden="true"
          />
          Error loading links: {error}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Links</h3>
        <CreateLinkButton shopId={shopId} refetch={loadLinks} />
      </div>
      
      {links.length === 0 ? (
        <div className="text-center p-8">
          <p className="text-sm text-muted-foreground">No links found</p>
        </div>
      ) : (
        <div className="space-y-2">
          {links.map((link: any) => (
            <LinkItem 
              key={link.id} 
              link={link} 
              onUpdate={loadLinks}
              onDelete={loadLinks}
            />
          ))}
        </div>
      )}
    </div>
  );
};

const LinkItem = ({ link, onUpdate, onDelete }: {
  link: any;
  onUpdate: () => void;
  onDelete: () => void;
}) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const { toast } = useToast();

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      const response = await deleteShopLink(link.id);
      
      if (response.success) {
        toast({
          title: "Link deleted successfully",
        });
        onDelete();
      } else {
        toast({
          title: "Failed to delete link",
          description: response.error || "Unknown error",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      toast({
        title: "Failed to delete link",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div className="border rounded-lg p-4">
      <div className="flex items-center justify-between">
        <div>
          <p className="font-medium">{link.channel.name}</p>
          <p className="text-sm text-muted-foreground">
            Created {new Date(link.createdAt).toLocaleDateString()}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleDelete}
            disabled={isDeleting}
            className="text-red-600 border-red-200 hover:bg-red-50"
          >
            {isDeleting ? "Deleting..." : "Delete"}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Links;