"use client";

import React, { useState, useEffect, useMemo } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Search, 
  Plus, 
  Minus, 
  Package, 
  DollarSign,
  AlertCircle,
  Check,
  X,
  ShoppingBag
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

interface LineItem {
  id: string;
  name: string;
  sku?: string;
  price?: number;
  image?: string;
  inventory?: number;
  available: boolean;
  shopId: string;
  shopName: string;
  productId: string;
  variantId?: string;
  description?: string;
}

interface SelectedLineItem extends LineItem {
  quantity: number;
}

interface LineItemSelectProps {
  shops: Array<{
    id: string;
    name: string;
    platform: { name: string };
  }>;
  onSelect: (items: SelectedLineItem[]) => void;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  preSelectedItems?: SelectedLineItem[];
}

export function LineItemSelect({ 
  shops, 
  onSelect, 
  isOpen, 
  onOpenChange,
  preSelectedItems = []
}: LineItemSelectProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedShop, setSelectedShop] = useState<string>("");
  const [lineItems, setLineItems] = useState<LineItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedItems, setSelectedItems] = useState<Map<string, SelectedLineItem>>(new Map());
  const { toast } = useToast();

  // Initialize with preselected items
  useEffect(() => {
    if (preSelectedItems.length > 0) {
      const itemsMap = new Map<string, SelectedLineItem>();
      preSelectedItems.forEach(item => {
        itemsMap.set(item.id, item);
      });
      setSelectedItems(itemsMap);
    }
  }, [preSelectedItems]);

  // Mock data for demonstration - in real implementation, this would fetch from API
  const mockLineItems: LineItem[] = [
    {
      id: "line1",
      name: "MacBook Pro 14-inch",
      sku: "MBP-14-2023",
      price: 1999.99,
      image: "/api/placeholder/64/64",
      inventory: 5,
      available: true,
      shopId: "shop1",
      shopName: "Apple Store",
      productId: "prod1",
      variantId: "var1",
      description: "Apple M2 Pro chip, 16GB RAM, 512GB SSD"
    },
    {
      id: "line2", 
      name: "iPhone 15 Pro",
      sku: "IP15P-128",
      price: 999.99,
      image: "/api/placeholder/64/64",
      inventory: 12,
      available: true,
      shopId: "shop1",
      shopName: "Apple Store",
      productId: "prod2",
      description: "128GB, Natural Titanium"
    },
    {
      id: "line3",
      name: "Samsung Galaxy S24",
      sku: "SGS24-256",
      price: 849.99,
      image: "/api/placeholder/64/64",
      inventory: 0,
      available: false,
      shopId: "shop2",
      shopName: "Samsung Direct",
      productId: "prod3",
      variantId: "var2",
      description: "256GB, Phantom Black"
    },
    {
      id: "line4",
      name: "AirPods Pro (3rd Gen)",
      sku: "APP3-USB",
      price: 249.99,
      image: "/api/placeholder/64/64",
      inventory: 25,
      available: true,
      shopId: "shop1",
      shopName: "Apple Store",
      productId: "prod4",
      description: "USB-C, Active Noise Cancellation"
    },
    {
      id: "line5",
      name: "Dell XPS 13",
      sku: "DXS13-I7",
      price: 1299.99,
      image: "/api/placeholder/64/64",
      inventory: 8,
      available: true,
      shopId: "shop3",
      shopName: "Dell Online",
      productId: "prod5",
      description: "Intel i7, 16GB RAM, 1TB SSD"
    }
  ];

  // Filter items based on search and selected shop
  const filteredItems = useMemo(() => {
    return mockLineItems.filter(item => {
      const matchesSearch = searchTerm === "" || 
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.sku?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.description?.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesShop = selectedShop === "" || item.shopId === selectedShop;
      
      return matchesSearch && matchesShop;
    });
  }, [searchTerm, selectedShop]);

  const updateQuantity = (item: LineItem, delta: number) => {
    setSelectedItems(prev => {
      const newMap = new Map(prev);
      const currentItem = newMap.get(item.id);
      const currentQty = currentItem?.quantity || 0;
      const newQty = Math.max(0, currentQty + delta);
      
      if (newQty === 0) {
        newMap.delete(item.id);
      } else {
        newMap.set(item.id, { ...item, quantity: newQty });
      }
      
      return newMap;
    });
  };

  const toggleItemSelection = (item: LineItem, checked: boolean) => {
    setSelectedItems(prev => {
      const newMap = new Map(prev);
      
      if (checked) {
        newMap.set(item.id, { ...item, quantity: 1 });
      } else {
        newMap.delete(item.id);
      }
      
      return newMap;
    });
  };

  const handleConfirmSelection = () => {
    const itemsToAdd = Array.from(selectedItems.values());
    
    if (itemsToAdd.length === 0) {
      toast({
        title: "No items selected",
        description: "Please select at least one item",
        variant: "destructive"
      });
      return;
    }

    // Validate availability
    const unavailableItems = itemsToAdd.filter(item => 
      !item.available || (item.inventory !== undefined && item.quantity > item.inventory)
    );

    if (unavailableItems.length > 0) {
      toast({
        title: "Availability issue",
        description: `Some items are not available or exceed inventory limits`,
        variant: "destructive"
      });
      return;
    }

    onSelect(itemsToAdd);

    toast({
      title: "Success",
      description: `Selected ${itemsToAdd.length} line item(s)`,
    });

    onOpenChange(false);
  };

  const handleClearAll = () => {
    setSelectedItems(new Map());
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const newMap = new Map<string, SelectedLineItem>();
      filteredItems.forEach(item => {
        if (item.available) {
          newMap.set(item.id, { ...item, quantity: 1 });
        }
      });
      setSelectedItems(newMap);
    } else {
      setSelectedItems(new Map());
    }
  };

  const totalSelectedItems = selectedItems.size;
  const totalQuantity = Array.from(selectedItems.values()).reduce((sum, item) => sum + item.quantity, 0);
  const totalValue = Array.from(selectedItems.values()).reduce(
    (sum, item) => sum + (item.price || 0) * item.quantity, 
    0
  );

  const availableItems = filteredItems.filter(item => item.available);
  const allAvailableSelected = availableItems.length > 0 && 
    availableItems.every(item => selectedItems.has(item.id));

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ShoppingBag className="h-5 w-5" />
            Select Line Items
          </DialogTitle>
          <DialogDescription>
            Search and select products from your connected shops to add as line items
          </DialogDescription>
        </DialogHeader>

        {/* Search and Filter Controls */}
        <div className="space-y-4">
          <div className="flex gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by product name, SKU, or description..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9"
              />
            </div>
            <Select value={selectedShop} onValueChange={setSelectedShop}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="All shops" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All shops</SelectItem>
                {shops.map((shop) => (
                  <SelectItem key={shop.id} value={shop.id}>
                    {shop.name} ({shop.platform.name})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Bulk Actions */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="select-all"
                  checked={allAvailableSelected}
                  onCheckedChange={handleSelectAll}
                />
                <label htmlFor="select-all" className="text-sm font-medium">
                  Select all available items
                </label>
              </div>
            </div>
            
            {totalSelectedItems > 0 && (
              <Button variant="outline" size="sm" onClick={handleClearAll}>
                Clear all
              </Button>
            )}
          </div>

          {/* Selection Summary */}
          {totalSelectedItems > 0 && (
            <div className="p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Package className="h-4 w-4" />
                  <span className="font-medium">
                    {totalSelectedItems} item{totalSelectedItems !== 1 ? 's' : ''} selected
                  </span>
                </div>
                <div className="flex items-center gap-4 text-sm">
                  <span>Total Qty: {totalQuantity}</span>
                  <span className="flex items-center gap-1">
                    <DollarSign className="h-3 w-3" />
                    {totalValue.toFixed(2)}
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Items List */}
        <div className="flex-1 overflow-auto space-y-2">
          {filteredItems.length === 0 ? (
            <div className="text-center py-8">
              <Package className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <p className="text-muted-foreground">
                {searchTerm || selectedShop ? "No items match your filters" : "No line items available"}
              </p>
            </div>
          ) : (
            filteredItems.map((item) => {
              const selectedItem = selectedItems.get(item.id);
              const isSelected = !!selectedItem;
              const quantity = selectedItem?.quantity || 0;
              const isOutOfStock = !item.available || (item.inventory !== undefined && item.inventory === 0);
              const exceedsInventory = item.inventory !== undefined && quantity > item.inventory;

              return (
                <Card key={item.id} className={`transition-colors ${isSelected ? 'ring-2 ring-blue-500' : ''} ${!item.available ? 'opacity-60' : ''}`}>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-4">
                      {/* Selection Checkbox */}
                      <Checkbox
                        checked={isSelected}
                        onCheckedChange={(checked) => toggleItemSelection(item, !!checked)}
                        disabled={!item.available}
                      />

                      {/* Product Image */}
                      <div className="w-16 h-16 bg-muted rounded-lg flex items-center justify-center">
                        {item.image ? (
                          <img
                            src={item.image}
                            alt={item.name}
                            className="w-full h-full object-cover rounded-lg"
                          />
                        ) : (
                          <Package className="h-6 w-6 text-muted-foreground" />
                        )}
                      </div>

                      {/* Product Info */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-medium">{item.name}</h4>
                            {item.description && (
                              <p className="text-sm text-muted-foreground mt-1">{item.description}</p>
                            )}
                            <div className="flex items-center gap-2 mt-2">
                              {item.sku && (
                                <Badge variant="outline" className="text-xs">
                                  {item.sku}
                                </Badge>
                              )}
                              <Badge variant="secondary" className="text-xs">
                                {item.shopName}
                              </Badge>
                            </div>
                          </div>
                          
                          <div className="text-right">
                            {item.price && (
                              <div className="flex items-center gap-1 font-medium">
                                <DollarSign className="h-3 w-3" />
                                {item.price.toFixed(2)}
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Availability Info */}
                        <div className="flex items-center gap-2 mt-2">
                          {isOutOfStock ? (
                            <div className="flex items-center gap-1 text-red-600">
                              <X className="h-3 w-3" />
                              <span className="text-xs">Out of stock</span>
                            </div>
                          ) : (
                            <div className="flex items-center gap-1 text-green-600">
                              <Check className="h-3 w-3" />
                              <span className="text-xs">
                                {item.inventory !== undefined ? `${item.inventory} in stock` : 'Available'}
                              </span>
                            </div>
                          )}
                          
                          {exceedsInventory && (
                            <div className="flex items-center gap-1 text-orange-600">
                              <AlertCircle className="h-3 w-3" />
                              <span className="text-xs">Exceeds inventory</span>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Quantity Controls */}
                      {isSelected && (
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => updateQuantity(item, -1)}
                            disabled={quantity <= 1}
                          >
                            <Minus className="h-3 w-3" />
                          </Button>
                          
                          <div className="w-12 text-center">
                            <span className="font-medium">{quantity}</span>
                          </div>
                          
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => updateQuantity(item, 1)}
                            disabled={item.inventory !== undefined && quantity >= item.inventory}
                          >
                            <Plus className="h-3 w-3" />
                          </Button>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              );
            })
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleConfirmSelection}
            disabled={totalSelectedItems === 0}
          >
            Add {totalSelectedItems > 0 ? `${totalSelectedItems} ` : ''}Line Item{totalSelectedItems !== 1 ? 's' : ''}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}