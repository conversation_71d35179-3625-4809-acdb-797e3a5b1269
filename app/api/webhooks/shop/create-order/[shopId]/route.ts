import { NextRequest, NextResponse } from 'next/server';
import { keystoneContext } from '@/features/keystone/context';
import { handleShopOrderWebhook } from '@/features/integrations/shop/lib/executor';

export async function POST(
  request: NextRequest,
  { params }: { params: { shopId: string } }
) {
  try {
    // Respond immediately to acknowledge receipt
    const response = NextResponse.json({ received: true });

    // Get the webhook payload
    const body = await request.json();
    const headers = Object.fromEntries(request.headers.entries());
    const { shopId } = params;

    // Process webhook asynchronously
    processWebhook(shopId, body, headers);

    return response;
  } catch (error) {
    console.error('Error processing create order webhook:', error);
    return NextResponse.json({ error: 'Webhook processing failed' }, { status: 500 });
  }
}

async function processWebhook(shopId: string, body: any, headers: any) {
  try {
    // Find the shop and its platform
    const shop = await keystoneContext.sudo().query.Shop.findOne({
      where: { id: shopId },
      query: `
        id
        domain
        accessToken
        user {
          id
          email
        }
        links {
          channel {
            id
            name
          }
        }
        platform {
          id
          name
          createOrderWebhookHandler
          appKey
          appSecret
        }
      `,
    });

    if (!shop) {
      console.error(`Shop not found: ${shopId}`);
      return;
    }

    console.log('Processing webhook for shop:', shop.domain);

    // Use the shop provider adapter to handle the webhook
    const orderData = await handleShopOrderWebhook({
      platform: {
        ...shop.platform,
        domain: shop.domain,
        accessToken: shop.accessToken,
      },
      event: body,
      headers,
    });

    console.log('Order data received from adapter:', orderData);

    // Create the order in the database
    const order = await keystoneContext.sudo().query.Order.createOne({
      data: {
        ...orderData,
        shop: { connect: { id: shop.id } },
        user: { connect: { id: shop.user.id } },
      },
      query: `
        id
        orderId
        orderName
        email
        first_name
        last_name
        streetAddress1
        streetAddress2
        city
        state
        zip
        phone
        totalPrice
        subTotalPrice
        totalDiscount
        totalTax
        status
        shop {
          id
          domain
          links {
            channel {
              id
              name
            }
          }
        }
      `,
    });

    console.log('Order created successfully:', order);
  } catch (error) {
    console.error('Error processing webhook:', error);
  }
}