'use server';

import { revalidatePath } from 'next/cache';
import { keystoneClient } from "@/features/dashboard/lib/keystoneClient";

// Interface for order data
interface Order {
  id: string;
  displayId: string;
  status: string;
  email: string;
  [key: string]: unknown;
}

/**
 * Get list of orders
 */
export async function getOrders(
  where: Record<string, unknown> = {},
  take: number = 10,
  skip: number = 0,
  orderBy: Array<Record<string, string>> = [{ createdAt: 'desc' }],
  selectedFields: string = `
    id
    orderId
    orderName
    email
    first_name
    last_name
    streetAddress1
    streetAddress2
    city
    state
    zip
    country
    phone
    currency
    totalPrice
    subTotalPrice
    totalDiscounts
    totalTax
    status
    error
    createdAt
    updatedAt
    user {
      id
      name
      email
    }
    shop {
      id
      name
      domain
      accessToken
    }
    lineItems {
      id
      name
      image
      price
      quantity
      productId
      variantId
      sku
      lineItemId
    }
    cartItems {
      id
      name
      image
      price
      quantity
      productId
      variantId
      sku
      purchaseId
      error
      channel {
        id
        name
      }
    }
  `
) {
  const query = `
    query GetOrders($where: OrderWhereInput, $take: Int!, $skip: Int!, $orderBy: [OrderOrderByInput!]) {
      items: orders(where: $where, take: $take, skip: $skip, orderBy: $orderBy) {
        ${selectedFields}
      }
      count: ordersCount(where: $where)
    }
  `;

  const response = await keystoneClient(query, { where, take, skip, orderBy });
  return response;
}

/**
 * Get a single order by ID with full details for order pages
 */
export async function getOrder(orderId: string) {
  const query = `
    query ($id: ID!) {
      order(where: { id: $id }) {
        id
        orderId
        orderName
        email
        first_name
        last_name
        streetAddress1
        streetAddress2
        city
        state
        zip
        country
        phone
        currency
        totalPrice
        subTotalPrice
        totalDiscounts
        totalTax
        linkOrder
        matchOrder
        processOrder
        status
        error
        orderMetadata
        createdAt
        updatedAt
        user {
          id
          name
          email
          phone
        }
        shop {
          id
          name
          domain
          accessToken
        }
        lineItems {
          id
          name
          image
          price
          quantity
          productId
          variantId
          sku
          lineItemId
        }
        cartItems {
          id
          name
          image
          price
          quantity
          productId
          variantId
          sku
          purchaseId
          error
          channel {
            id
            name
          }
        }
      }
    }
  `;

  const cacheOptions = {
    next: {
      tags: [`order-${orderId}`],
      revalidate: 3600, // Cache for 1 hour
    },
  };

  const response = await keystoneClient(query, { id: orderId }, cacheOptions);
  return response;
}

/**
 * Update order status
 */
export async function updateOrderStatus(id: string, status: string) {
  const query = `
    mutation UpdateOrderStatus($id: ID!, $data: OrderUpdateInput!) {
      updateOrder(where: { id: $id }, data: $data) {
        id
        status
      }
    }
  `;

  const response = await keystoneClient(query, {
    id,
    data: { status }
  });

  if (response.success) {
    revalidatePath(`/dashboard/platform/orders/${id}`);
  } else {
    console.error(`Failed to update order status for ${id}:`, response.error);
  }

  return response;
}

/**
 * Get filtered orders based on status and search parameters
 */
export async function getFilteredOrders(
  status: string | null = null,
  search: string | null = null,
  page: number = 1,
  pageSize: number = 10,
  sort: { field: string; direction: 'ASC' | 'DESC' } | null = null
) {
  const where: Record<string, unknown> = {};

  // Add status filter if provided and not 'all'
  if (status && status !== 'all') {
    where.status = { equals: status };
  }

  // Add search filter if provided
  if (search) {
    where.OR = [
      { orderId: { contains: search, mode: 'insensitive' } },
      { orderName: { contains: search, mode: 'insensitive' } },
      { email: { contains: search, mode: 'insensitive' } },
      { first_name: { contains: search, mode: 'insensitive' } },
      { last_name: { contains: search, mode: 'insensitive' } },
      { user: { name: { contains: search, mode: 'insensitive' } } },
      { user: { email: { contains: search, mode: 'insensitive' } } },
    ];
  }

  // Calculate pagination
  const skip = (page - 1) * pageSize;

  // Handle sorting
  const orderBy = sort
    ? [{ [sort.field]: sort.direction.toLowerCase() }]
    : [{ createdAt: 'desc' }];

  return getOrders(where, pageSize, skip, orderBy);
}

/**
 * Get order counts by status
 */
export async function getOrderStatusCounts() {
  const query = `
    query GetOrderStatusCounts {
      pendingCount: ordersCount(where: { status: { equals: "PENDING" } })
      inprocessCount: ordersCount(where: { status: { equals: "INPROCESS" } })
      awaitingCount: ordersCount(where: { status: { equals: "AWAITING" } })
      backorderedCount: ordersCount(where: { status: { equals: "BACKORDERED" } })
      cancelledCount: ordersCount(where: { status: { equals: "CANCELLED" } })
      completeCount: ordersCount(where: { status: { equals: "COMPLETE" } })
      all: ordersCount
    }
  `;

  const response = await keystoneClient(query);
  return response;
}
/**
 * Search for products in a channel
 */
export async function searchChannelProducts(channelId: string, searchEntry: string) {
  const query = `
    query SearchChannelProducts($channelId: ID!, $searchEntry: String!) {
      searchChannelProducts(channelId: $channelId, searchEntry: $searchEntry) {
        image
        title
        productId
        variantId
        price
      }
    }
  `;

  const response = await keystoneClient(query, { channelId, searchEntry });

  if (response.error) {
    throw new Error(response.error);
  }

  return response.data?.searchChannelProducts;
}

/**
 * Add item to cart
 */
export async function addToCart(itemData: any) {
    const query = `
        mutation ADD_TO_CART_MUTATION(
            $channelId: ID
            $image: String
            $name: String
            $price: String
            $productId: String
            $variantId: String
            $quantity: String
            $orderId: ID
        ) {
            addToCart(
                channelId: $channelId
                image: $image
                name: $name
                price: $price
                productId: $productId
                variantId: $variantId
                quantity: $quantity
                orderId: $orderId
            ) {
                id
            }
        }
    `;

    const response = await keystoneClient(query, itemData);
    if (response.success) {
        revalidatePath(`/dashboard/platform/orders/${itemData.orderId}`);
    }
    return response;
}

/**
 * Match order
 */
export async function matchOrder(orderId: string) {
    const query = `
        mutation MATCHORDER_MUTATION($orderId: ID!) {
            matchOrder(orderId: $orderId) {
                id
            }
        }
    `;
    const response = await keystoneClient(query, { orderId });
    if (response.success) {
        revalidatePath(`/dashboard/platform/orders/${orderId}`);
    }
    return response;
}


/**
 * Add match to cart (GET MATCH functionality)
 */
export async function addMatchToCart(orderId: string) {
    const query = `
        mutation ADDMATCHTOCART_MUTATION($orderId: ID!) {
            addMatchToCart(orderId: $orderId) {
                id
            }
        }
    `;
    const response = await keystoneClient(query, { orderId });
    if (response.success) {
        revalidatePath(`/dashboard/platform/orders/${orderId}`);
    }
    return response;
}


/**
 * Place orders
 */
export async function placeOrders(ids: string[]) {
    const query = `
        mutation PLACE_ORDERS($ids: [ID!]!) {
            placeOrders(ids: $ids) {
                orderId
            }
        }
    `;
    const response = await keystoneClient(query, { ids });
    if (response.success) {
        revalidatePath(`/dashboard/platform/orders`);
    }
    return response;
}
/**
 * Get all channels
 */
export async function getChannels() {
  const query = `
    query GetChannels {
      channels {
        id
        name
      }
    }
  `;
  const response = await keystoneClient(query);
  return response.data?.channels || [];
}

/**
 * Delete many orders
 */
export async function deleteManyOrders(ids: string[]) {
  const query = `
    mutation DeleteManyOrders($ids: [ID!]!) {
      deleteOrders(where: { id: { in: $ids } }) {
        id
      }
    }
  `;
  const response = await keystoneClient(query, { ids });
  if (response.success) {
    revalidatePath(`/dashboard/platform/orders`);
  }
  return response;
}