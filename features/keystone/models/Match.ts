import { list } from "@keystone-6/core";
import { allOperations } from "@keystone-6/core/access";
import { relationship, timestamp, virtual } from "@keystone-6/core/fields";
import { graphql } from "@keystone-6/core";

import { isSignedIn, permissions, rules } from "../access";
import { trackingFields } from "./trackingFields";

export const Match = list({
  access: {
    operation: {
      create: permissions.canCreateMatches,
      query: isSignedIn,
      update: isSignedIn,
      delete: permissions.canManageMatches,
    },
    filter: {
      query: rules.canReadMatches,
      update: rules.canManageMatches,
      delete: rules.canManageMatches,
    },
  },
  hooks: {
    resolveInput: {
      create: ({ operation, resolvedData, context }) => {
        // Auto-assign user if not provided
        if (!resolvedData.user && context.session?.itemId) {
          return {
            ...resolvedData,
            user: { connect: { id: context.session.itemId } },
          };
        }
        return resolvedData;
      },
    },
    // TODO: Add complex match validation hooks from OpenShip
    // beforeOperation: async ({ operation, resolvedData, context }) => {
    //   // Ensure items exist before creating matches
    // },
  },
  ui: {
    listView: {
      initialColumns: ["input", "output", "user"],
    },
  },
  fields: {
    // Virtual fields for match status
    outputPriceChanged: virtual({
      field: graphql.field({
        type: graphql.String,
        resolve() {
          return "Price change detection for output items";
        },
      }),
      ui: {
        itemView: { fieldMode: "read" },
        listView: { fieldMode: "hidden" },
      },
    }),
    inventoryNeedsToBeSynced: virtual({
      field: graphql.field({
        type: graphql.String,
        resolve() {
          return "Inventory sync status check";
        },
      }),
      ui: {
        itemView: { fieldMode: "read" },
        listView: { fieldMode: "hidden" },
      },
    }),

    // Relationships - Many-to-many between ShopItems and ChannelItems
    input: relationship({
      ref: "ShopItem.matches",
      many: true,
      ui: {
        displayMode: "cards",
        cardFields: ["productId", "variantId", "quantity"],
        inlineConnect: true,
      },
    }),
    output: relationship({
      ref: "ChannelItem.matches",
      many: true,
      ui: {
        displayMode: "cards",
        cardFields: ["productId", "variantId", "quantity", "price"],
        inlineConnect: true,
      },
    }),
    user: relationship({
      ref: "User.matches",
    }),

    ...trackingFields,
  },
});
