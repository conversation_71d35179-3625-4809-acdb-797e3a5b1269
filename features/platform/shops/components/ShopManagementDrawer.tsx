"use client";

import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON><PERSON><PERSON>,
} from "@/components/ui/drawer";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge-button";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Ticket,
  SquareStack,
  LinkIcon,
  Webhook,
  Store,
  ArrowLeft,
  ArrowRight,
} from "lucide-react";
import Link from "next/link";
import { SearchOrders } from "./SearchOrders";
import { Links } from "./Links";
import { Webhooks } from "./Webhooks";
import MatchList from "./MatchList";
import type { Shop } from "../lib/types";

interface ShopManagementDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  shop: Shop | undefined;
}

export function ShopManagementDrawer({
  open,
  onOpenChange,
  shop,
}: ShopManagementDrawerProps) {
  const handleOrderCreated = (order: any) => {
    // You could show a success message or redirect to the order
    console.log("Order created:", order);
    // Optionally close the drawer
    // onOpenChange(false);
  };

  if (!shop) return null;

  return (
    <Drawer open={open} onOpenChange={onOpenChange}>
      <DrawerContent className="overflow-x-hidden sm:max-w-lg">
        <DrawerHeader className="-px-6 w-full">
          <DrawerTitle className="flex w-full items-start justify-between">
            <div className="flex flex-col gap-3">
              <span>{shop.name}</span>

              <div className="flex items-center gap-3">
                {shop.platform && (
                  <Badge color="zinc" className="text-xs border py-0.5">
                    {shop.platform.name}
                  </Badge>
                )}
                <span className="text-sm text-muted-foreground font-normal">
                  {new Date(shop.createdAt).toLocaleDateString("en-US", {
                    year: "numeric",
                    month: "short",
                    day: "numeric",
                  })}
                </span>
              </div>
            </div>
          </DrawerTitle>
        </DrawerHeader>
        <DrawerBody className="-mx-6 overflow-y-scroll">
          <Tabs defaultValue="orders">
            <TabsList className="justify-start w-full h-auto gap-2 rounded-none bg-transparent px-6 py-0 border-b border-border">
              <TabsTrigger
                value="orders"
                className="text-muted-foreground relative pb-3 pt-2 px-3 data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:text-foreground hover:text-foreground transition-colors after:absolute after:bottom-0 after:left-0 after:right-0 after:h-[2px] data-[state=active]:after:bg-foreground rounded-none"
              >
                <Ticket className="-ms-0.5 me-1.5 opacity-60" size={16} />
                <span className="hidden sm:inline">Orders</span>
              </TabsTrigger>
              <TabsTrigger
                value="matches"
                className="text-muted-foreground relative pb-3 pt-2 px-3 data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:text-foreground hover:text-foreground transition-colors after:absolute after:bottom-0 after:left-0 after:right-0 after:h-[2px] data-[state=active]:after:bg-foreground rounded-none"
              >
                <SquareStack className="-ms-0.5 me-1.5 opacity-60" size={16} />
                <span className="hidden sm:inline">Matches</span>
              </TabsTrigger>
              <TabsTrigger
                value="links"
                className="text-muted-foreground relative pb-3 pt-2 px-3 data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:text-foreground hover:text-foreground transition-colors after:absolute after:bottom-0 after:left-0 after:right-0 after:h-[2px] data-[state=active]:after:bg-foreground rounded-none"
              >
                <LinkIcon className="-ms-0.5 me-1.5 opacity-60" size={16} />
                <span className="hidden sm:inline">Links</span>
              </TabsTrigger>
              <TabsTrigger
                value="webhooks"
                className="text-muted-foreground relative pb-3 pt-2 px-3 data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:text-foreground hover:text-foreground transition-colors after:absolute after:bottom-0 after:left-0 after:right-0 after:h-[2px] data-[state=active]:after:bg-foreground rounded-none"
              >
                <Webhook className="-ms-0.5 me-1.5 opacity-60" size={16} />
                <span className="hidden sm:inline">Webhooks</span>
              </TabsTrigger>
            </TabsList>
            <TabsContent value="orders" className="space-y-0">
              <div className="px-6">
                <div className="flex-shrink-0">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="flex gap-1">
                      <Button
                        variant="outline"
                        size="icon"
                        className="h-9.5 w-9.5"
                        disabled
                      >
                        <ArrowLeft className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="icon"
                        className="h-9.5 w-9.5"
                        disabled
                      >
                        <ArrowRight className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="w-full flex rounded-lg shadow-sm">
                      <Input
                        className="h-10 -me-px flex-1 rounded-lg rounded-e-none shadow-none focus-visible:z-10"
                        placeholder="Search orders..."
                        type="text"
                      />
                      <button className="tracking-wide inline-flex items-center rounded-e-lg border border-input bg-background px-3 text-xs font-semibold text-muted-foreground transition-colors hover:bg-accent hover:text-foreground">
                        SEARCH
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <SearchOrders
                shopId={shop.id}
                searchEntry=""
                pageSize={10}
                hideSearchBar={true}
                onOrderCreated={handleOrderCreated}
              />
            </TabsContent>
            <TabsContent value="matches" className="space-y-6 px-6">
              <MatchList
                shopId={shop.id}
                onMatchAction={() => {}}
                showCreate={false}
              />
            </TabsContent>
            <TabsContent value="links" className="space-y-6 px-6">
              <Links shopId={shop.id} />
            </TabsContent>
            <TabsContent value="webhooks" className="space-y-6 px-6">
              <div className="space-y-4">
                <div className="text-sm text-muted-foreground mb-4">
                  Create platform webhooks to keep Openship in sync
                </div>
                <Webhooks shopId={shop.id} />
              </div>
            </TabsContent>
          </Tabs>
        </DrawerBody>
        <DrawerFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}
