# CLAUDE.md - OpenShip 4 Development Guide

This file provides guidance to <PERSON> (claude.ai/code) when working with OpenShip 4.

## Project Overview

OpenShip 4 is the fourth iteration of the OpenShip platform, building upon the foundation established in OpenShip Dasher. This is a Next.js 15 + KeystoneJS 6 application focused on e-commerce platform management with a sophisticated dashboard architecture.

## Development Commands

- `npm run dev` - Build Keystone + migrate + start Next.js dev server
- `npm run build` - Build Keystone + migrate + build Next.js for production
- `npm run migrate:gen` - Generate and apply new database migrations
- `npm run migrate` - Deploy existing migrations to database

## Architecture Overview

### Core Architecture
This is a Next.js 15 + KeystoneJS 6 application with a **dual dashboard architecture**:

- **Backend**: KeystoneJS 6 provides GraphQL API, authentication, and database operations
- **Frontend**: Two parallel admin interfaces sharing the same backend
  - `dashboard/` - Original KeystoneJS implementation (feature-complete)
  - `dashboard2/` - Refactored implementation (work in progress)

### Platform Pages Architecture
OpenShip 4 implements a **platform pages system** that extends the dashboard functionality:

- **Dashboard Pages**: Internal CMS-style management (list, item, create pages)
- **Platform Pages**: Customer-facing interface adapted from dashboard architecture
- **Shared Components**: Both systems share the same backend models and GraphQL API
- **Adaptive UI**: Platform pages are adapted versions of dashboard pages with customer-specific UI/UX

## Key Directories

### Backend Structure
- `features/keystone/` - Backend configuration
  - `models/` - Keystone list definitions (User, Role, Todo, Orders, Products, etc.)
  - `access.ts` - Role-based permission logic
  - `mutations/` - Custom GraphQL mutations

### Dashboard Structure
- `features/dashboard/` - Original admin interface
  - `actions/` - Server actions for data operations
  - `components/` - Reusable UI components
  - `screens/` - Page-level components
  - `views/` - Field type implementations

- `features/dashboard2/` - Refactored admin interface (in development)
  - More modular architecture with improved TypeScript
  - Better separation of concerns
  - Enhanced component reusability

### Platform Structure (To Be Implemented)
- `features/platform/` - Customer-facing platform pages
  - `components/` - Platform-specific UI components
  - `pages/` - Platform page implementations (orders, products, etc.)
  - `actions/` - Platform-specific server actions
  - `hooks/` - Platform-specific data fetching hooks

### App Router Structure
- `app/` - Next.js App Router with parallel routes
  - `(dashboard)/` - Admin dashboard routes
  - `(platform)/` - Customer platform routes
  - Shared layouts and middleware

## Data Models & Permissions

### Core Models
- **User**: Authentication and user management
- **Role**: Role-based access control
- **Todo**: Task management (example model)
- **Orders**: E-commerce order management
- **Products**: Product catalog management
- **Shops**: Multi-tenant shop management

### Permission System
Role-based access control with granular permissions:
- **Dashboard Permissions**: `canAccessDashboard`, `canManagePeople`, `canManageRoles`
- **Task Permissions**: `canCreateTodos`, `canManageAllTodos`
- **User Permissions**: `canSeeOtherPeople`, `canEditOtherPeople`
- **Platform Permissions**: Shop-specific access controls for platform users

## Architecture Patterns

### Field Controller Pattern
KeystoneJS uses field controllers that handle:
- Data serialization/deserialization
- Validation logic
- GraphQL selection building
- React component rendering

### Conditional Field Modes
Fields adapt behavior based on:
- User permissions
- Other field values
- Create vs update context
- Dashboard vs platform context

### GraphQL Integration
- Dynamic query building from field controllers
- SWR for client-side data fetching
- Server actions for mutations
- Optimistic updates for better UX

### Platform Page Pattern
Platform pages follow a consistent pattern:
1. **Base Component**: Shared logic from dashboard pages
2. **Platform Adapter**: Custom UI/UX for customer-facing interface
3. **GraphQL Integration**: Same queries as dashboard with platform-specific selections
4. **Permission Context**: Platform-specific access controls

## Tech Stack

### Frontend
- **Framework**: Next.js 15 (App Router)
- **React**: React 19 with Server Components
- **TypeScript**: Full type safety
- **UI Components**: Radix UI primitives
- **Styling**: Tailwind CSS
- **Icons**: Lucide React

### Backend
- **CMS**: KeystoneJS 6
- **Database**: Prisma ORM with PostgreSQL
- **API**: GraphQL (GraphQL Yoga)
- **Authentication**: KeystoneJS built-in auth

### Data Layer
- **Client State**: SWR for data fetching
- **Server State**: KeystoneJS session management
- **Caching**: Next.js built-in caching + SWR

## Related Projects & References

### OpenShip Dasher
- **Purpose**: Previous iteration with established platform pages
- **Key Features**: Mature platform implementation
- **Reference Use**: Copy platform architecture patterns
- **Location**: Separate repository - OpenShip Dasher

### OpenFront Final 2
- **Purpose**: Sister application with updated platform pages
- **Key Component**: Order platform page implementation
- **Reference Use**: Modern platform page patterns to copy
- **Priority**: Copy order platform page as starting point
- **Location**: Separate repository - OpenFront Final 2

## Current Development Status

### Dashboard2 Progress
**Status**: Core functionality complete, UI refinements needed

**Completed**:
- ✅ Create pages with proper responsive layout
- ✅ Fixed cell rendering issues (relationship, password, document, checkbox)
- ✅ Fixed search functionality and GraphQL queries
- ✅ Improved filter UI with sophisticated filter pills
- ✅ Fixed TypeScript errors and type definitions

**Remaining Issues**:
- [ ] Item page UI layout - Copy Dashboard1's button positioning
- [ ] Dashboard home page - Fix server rendering and model counts
- [ ] Sidebar user UI - Add avatar, email, dropdown functionality

### Platform Pages Development
**Status**: Not started - Priority after Dashboard2 completion

**Immediate Next Steps**:
1. **Research Phase**: Study OpenFront Final 2's order platform page
2. **Copy Implementation**: Bring order platform page to OpenShip 4
3. **Adapt Components**: Modify for OpenShip's specific models and GraphQL
4. **Reference OpenShip Dasher**: Compare with existing platform patterns
5. **Implement Additional Pages**: Extend to other models as needed

**Platform Page Requirements**:
- Customer-facing UI adapted from dashboard pages
- Same GraphQL backend with platform-specific queries
- Responsive design for customer use
- Integration with existing authentication/permissions
- Consistent with OpenShip branding and UX

## Development Workflow

### For Dashboard Work
1. Use Dashboard2 as primary development target
2. Reference Dashboard1 for UI patterns and functionality
3. Maintain backward compatibility with existing GraphQL API
4. Follow KeystoneJS patterns for new field types

### For Platform Pages
1. **Start with OpenFront Final 2**: Copy order platform page structure
2. **Reference OpenShip Dasher**: Compare with established patterns
3. **Adapt for OpenShip**: Modify components and queries for OpenShip models
4. **Test Integration**: Ensure proper GraphQL integration
5. **Extend to Other Models**: Apply pattern to additional platform pages

## Important Notes

### GraphQL API
- Endpoint: `/api/graphql`
- Both dashboards and platform pages share the same backend
- Use server actions for mutations in components
- Field implementations follow KeystoneJS controller patterns

### Permissions
- Permission checks integrated throughout UI layer
- Platform pages need customer-specific permission logic
- Dashboard permissions separate from platform permissions

### Development Best Practices
- Always test both dashboard and platform functionality
- Maintain consistent UI patterns across platform pages
- Follow established GraphQL query patterns
- Use TypeScript for all new implementations
- Reference existing implementations before creating new patterns

---

**OpenShip 4** | E-commerce Platform | Next.js 15 + KeystoneJS 6 | Dashboard + Platform Architecture