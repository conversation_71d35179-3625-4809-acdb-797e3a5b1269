"use client";

import * as React from "react";
import {
  ChevronRight,
  LayoutDashboard,
  ArrowUpRight,
  Sparkles,
  Package,
  Users,
  Tag,
  Gift,
  BadgeDollarSign,
  Clipboard,
  BarChart3,
  LayoutList,
  ArrowLeftRight,
  ShieldCheck,
  Truck,
  Settings,
  Ticket,
} from "lucide-react";
import { usePathname } from "next/navigation";

import Link from "next/link";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from "@/components/ui/sidebar";

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

// Removed unused useTheme import
import { Logo, LogoIcon } from '@/features/dashboard/components/Logo';
import { basePath } from "@/features/dashboard/lib/config";
import { LucideIcon } from "lucide-react";
import { UserProfileClient } from "@/features/dashboard/components/UserProfileClient";
import type { User } from "@/features/dashboard/components/DashboardUI";
import { PlatformSidebarLinks, PlatformSidebarGroups, getIconForNavItem } from "@/features/platform/components";
import { OnboardingCards, OnboardingDialog, dismissOnboarding, startOnboarding } from "@/features/platform/onboarding";

interface AppSidebarProps {
  sidebarLinks: Array<{ title: string; href: string }>;
  user?: User | null;
}

export function AppSidebar({ sidebarLinks = [], user }: AppSidebarProps) {
  const { isMobile, setOpenMobile } = useSidebar();
  const pathname = usePathname();
  const [isOnboardingDialogOpen, setIsOnboardingDialogOpen] = React.useState(false);

  // Function to check if a link is active
  const isLinkActive = React.useCallback(
    (href: string) => {
      if (!pathname) return false;

      // Exact match for dashboard root
      if (
        href === `${basePath}/dashboard` &&
        pathname === `${basePath}/dashboard`
      ) {
        return true;
      }

      // For other pages, check if the pathname starts with the href
      // This handles nested routes like /dashboard/products/1
      if (href !== `${basePath}/dashboard`) {
        return pathname.startsWith(href);
      }

      return false;
    },
    [pathname]
  );

  // Get platform groups for organized navigation
  const platformGroups = React.useMemo(() => {
    const groups = PlatformSidebarGroups({ basePath });
    // Add onboarding to system group when dismissed
    if (user?.onboardingStatus === 'dismissed') {
      const systemGroup = groups.find(g => g.id === 'system');
      if (systemGroup) {
        systemGroup.items.push({
          title: 'Onboarding',
          href: '#onboarding',
        });
      }
    }
    return groups;
  }, [user?.onboardingStatus]);

  // Get flat platform links for filtering model links
  const platformLinks = React.useMemo(() => {
    return PlatformSidebarLinks({ basePath });
  }, []);

  // Filter sidebar links to only include model links (non-platform links)
  const modelLinks = React.useMemo(() => {
    const platformPaths = platformLinks.map(link => link.href);
    return sidebarLinks.filter(link => !platformPaths.includes(link.href));
  }, [sidebarLinks, platformLinks]);

  // Dashboard items for the collapsible menu
  const dashboardItems = [
    {
      title: "Platform",
      groups: platformGroups,
      isActive: true,
      icon: LayoutDashboard,
    },
    {
      title: "Models",
      items: modelLinks,
      isActive: false,
      icon: Package,
    },
  ];

  // Note: We're not using a home link in the current implementation
  // but keeping the structure for future reference

  return (
    <Sidebar collapsible="icon">
      <SidebarHeader>
        <SidebarMenuButton asChild>
          <div className="group-has-[[data-collapsible=icon]]/sidebar-wrapper:hidden p-2">
            <Logo />
          </div>
        </SidebarMenuButton>
        <SidebarMenuButton asChild>
          <div className="hidden group-has-[[data-collapsible=icon]]/sidebar-wrapper:block">
            <LogoIcon />
          </div>
        </SidebarMenuButton>
      </SidebarHeader>
      <SidebarContent className="no-scrollbar">
        {/* Main Navigation */}
        {/* <SidebarGroup>
          <SidebarGroupLabel>Platform</SidebarGroupLabel>
          <SidebarMenu>
            {mainNavItems.map((route) => (
              <SidebarMenuItem key={route.url}>
                <SidebarMenuButton asChild>
                  <Link href={route.url} onClick={() => setOpenMobile(false)}>
                    {route.icon && <route.icon className="h-4 w-4 stroke-2" />}
                    <span>{route.title}</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            ))}
          </SidebarMenu>
        </SidebarGroup> */}

        {/* Dashboard Links - Collapsible/Dropdown */}
        {dashboardItems.map((dashboardItem) => (
          <SidebarGroup key={dashboardItem.title}>
            <SidebarGroupLabel>{dashboardItem.title}</SidebarGroupLabel>
            <div className="max-h-full overflow-y-auto group-has-[[data-collapsible=icon]]/sidebar-wrapper:hidden">
              <Collapsible
                key={dashboardItem.title}
                asChild
                defaultOpen={dashboardItem.isActive}
                className="group/collapsible"
              >
                <SidebarMenuItem>
                  <CollapsibleTrigger asChild>
                    <SidebarMenuButton>
                      <dashboardItem.icon className="h-4 w-4" />
                      <span>{dashboardItem.title}</span>
                      <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                    </SidebarMenuButton>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <SidebarMenuSub>
                      {/* Platform has groups, Models has items */}
                      {dashboardItem.title === "Platform" && 'groups' in dashboardItem ? (
                        // Render platform groups with nested items
                        dashboardItem.groups.map((group) => (
                          <Collapsible
                            key={group.id}
                            asChild
                            defaultOpen={true}
                            className="group/group-collapsible"
                          >
                            <SidebarMenuSubItem>
                              <CollapsibleTrigger asChild>
                                <SidebarMenuSubButton className="font-medium text-sidebar-foreground">
                                  <group.icon className="h-4 w-4" />
                                  <span>{group.title}</span>
                                  <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/group-collapsible:rotate-90" />
                                </SidebarMenuSubButton>
                              </CollapsibleTrigger>
                              <CollapsibleContent>
                                <div className="ml-4 space-y-1">
                                  {group.items.map((link) => {
                                    const Icon = getIconForNavItem(link.title);
                                    const handleClick = (e: React.MouseEvent) => {
                                      if (link.title === 'Onboarding') {
                                        e.preventDefault();
                                        setIsOnboardingDialogOpen(true);
                                      }
                                      setOpenMobile(false);
                                    };

                                    return (
                                      <SidebarMenuSubButton
                                        key={link.href}
                                        asChild
                                        isActive={isLinkActive(link.href)}
                                        className="text-sm"
                                      >
                                        <Link href={link.href} onClick={handleClick}>
                                          {Icon && <Icon className="h-3 w-3 mr-2" />}
                                          <span>{link.title}</span>
                                        </Link>
                                      </SidebarMenuSubButton>
                                    );
                                  })}
                                </div>
                              </CollapsibleContent>
                            </SidebarMenuSubItem>
                          </Collapsible>
                        ))
                      ) : (
                        // Render regular items for Models
                        ('items' in dashboardItem ? dashboardItem.items : []).map((link) => {
                          const Icon = dashboardItem.title === "Platform" ? getIconForNavItem(link.title) : undefined;
                          const handleClick = (e: React.MouseEvent) => {
                            if (link.title === 'Onboarding') {
                              e.preventDefault();
                              setIsOnboardingDialogOpen(true);
                            }
                            setOpenMobile(false);
                          };

                          return (
                            <SidebarMenuSubItem key={link.href}>
                              <SidebarMenuSubButton
                                asChild
                                isActive={isLinkActive(link.href)}
                              >
                                <Link href={link.href} onClick={handleClick}>
                                  {Icon && <Icon className="h-4 w-4 mr-2" />}
                                  <span>{link.title}</span>
                                </Link>
                              </SidebarMenuSubButton>
                            </SidebarMenuSubItem>
                          );
                        })
                      )}
                    </SidebarMenuSub>
                  </CollapsibleContent>
                </SidebarMenuItem>
              </Collapsible>
            </div>

            <div className="hidden group-has-[[data-collapsible=icon]]/sidebar-wrapper:block">
              <DropdownMenu>
                <SidebarMenuItem>
                  <DropdownMenuTrigger asChild>
                    <SidebarMenuButton className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground">
                      <dashboardItem.icon className="h-4 w-4" />
                    </SidebarMenuButton>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                    side={isMobile ? "bottom" : "right"}
                    align={isMobile ? "end" : "start"}
                    className="min-w-56"
                  >
                    <div className="max-h-[calc(100vh-16rem)] overflow-y-auto py-1">
                      {/* Platform has groups, Models has items */}
                      {dashboardItem.title === "Platform" && 'groups' in dashboardItem ? (
                        // Render platform groups with items in dropdown
                        dashboardItem.groups.map((group) => (
                          <div key={group.id}>
                            <div className="px-2 py-1.5 text-sm font-medium text-muted-foreground border-b mb-1">
                              <div className="flex items-center gap-2">
                                <group.icon className="h-3 w-3" />
                                {group.title}
                              </div>
                            </div>
                            {group.items.map((link) => {
                              const Icon = getIconForNavItem(link.title);
                              const handleClick = (e: React.MouseEvent) => {
                                if (link.title === 'Onboarding') {
                                  e.preventDefault();
                                  setIsOnboardingDialogOpen(true);
                                }
                                setOpenMobile(false);
                              };

                              return (
                                <DropdownMenuItem
                                  asChild
                                  key={link.href}
                                  className={
                                    isLinkActive(link.href)
                                      ? "bg-blue-50 text-blue-600 ml-2"
                                      : "ml-2"
                                  }
                                >
                                  <Link href={link.href} onClick={handleClick}>
                                    {Icon && <Icon className="h-4 w-4 mr-2" />}
                                    <span>{link.title}</span>
                                    {isLinkActive(link.href) && (
                                      <div className="ml-auto h-2 w-2 rounded-full bg-blue-600" />
                                    )}
                                  </Link>
                                </DropdownMenuItem>
                              );
                            })}
                          </div>
                        ))
                      ) : (
                        // Render regular items for Models
                        ('items' in dashboardItem ? dashboardItem.items : []).map((link) => {
                          const Icon = dashboardItem.title === "Platform" ? getIconForNavItem(link.title) : undefined;
                          const handleClick = (e: React.MouseEvent) => {
                            if (link.title === 'Onboarding') {
                              e.preventDefault();
                              setIsOnboardingDialogOpen(true);
                            }
                            setOpenMobile(false);
                          };

                          return (
                            <DropdownMenuItem
                              asChild
                              key={link.href}
                              className={
                                isLinkActive(link.href)
                                  ? "bg-blue-50 text-blue-600"
                                  : ""
                              }
                            >
                              <Link href={link.href} onClick={handleClick}>
                                {Icon && <Icon className="h-4 w-4 mr-2" />}
                                <span>{link.title}</span>
                                {isLinkActive(link.href) && (
                                  <div className="ml-auto h-2 w-2 rounded-full bg-blue-600" />
                                )}
                              </Link>
                            </DropdownMenuItem>
                          );
                        })
                      )}
                    </div>
                  </DropdownMenuContent>
                </SidebarMenuItem>
              </DropdownMenu>
            </div>
          </SidebarGroup>
        ))}
      </SidebarContent>
      <SidebarFooter>
        {/* User Profile Section */}

        <SidebarMenu>
          {/* Onboarding Section */}
          <div className="w-full mb-2 overflow-visible">
            <OnboardingCards
              steps={[
                {
                  href: '#onboarding',
                  title: 'Welcome to Openfront',
                  description:
                    'Your store is empty. Click get started to configure your store with products, categories, and regions.',
                },
              ]}
              onboardingStatus={user?.onboardingStatus}
              userRole={user?.role}
              onDismiss={async () => {
                try {
                  const result = await dismissOnboarding();
                  if (!result.success) {
                    console.error('Error dismissing onboarding:', result.error);
                  }
                  // No need to reload the page - revalidatePath handles the update
                } catch (error) {
                  console.error('Error dismissing onboarding:', error);
                }
              }}
              onOpenDialog={() => setIsOnboardingDialogOpen(true)}
            />
          </div>
        </SidebarMenu>
        
        {user && <UserProfileClient user={user} />}

      </SidebarFooter>
      <SidebarRail />
      <OnboardingDialog
        isOpen={isOnboardingDialogOpen}
        onClose={() => setIsOnboardingDialogOpen(false)}
      />
    </Sidebar>
  );
}
