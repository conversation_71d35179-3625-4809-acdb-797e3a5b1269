import { list } from "@keystone-6/core";
import { allOperations } from "@keystone-6/core/access";
import { relationship, text, timestamp } from "@keystone-6/core/fields";

import { isSignedIn, permissions, rules } from "../access";
import { trackingFields } from "./trackingFields";

export const TrackingDetail = list({
  access: {
    operation: {
      create: isSignedIn,
      query: isSignedIn,
      update: isSignedIn,
      delete: permissions.canManageOrders,
    },
    filter: {
      query: rules.canReadOrders,
      update: rules.canManageOrders,
      delete: rules.canManageOrders,
    },
  },
  hooks: {
    resolveInput: {
      create: ({ operation, resolvedData, context }) => {
        // Auto-assign user if not provided
        if (!resolvedData.user && context.session?.itemId) {
          return {
            ...resolvedData,
            user: { connect: { id: context.session.itemId } },
          };
        }
        return resolvedData;
      },
    },
    // TODO: Add complex tracking hooks from OpenShip
    // afterOperation: async ({ operation, item, context }) => {
    //   // Auto-connect to cart items by purchaseId
    //   // Call platform tracking functions
    //   // Update order status when all items tracked
    // },
  },
  ui: {
    listView: {
      initialColumns: ["trackingCompany", "trackingNumber", "purchaseId"],
    },
  },
  fields: {
    // Tracking information
    trackingCompany: text({
      validation: { isRequired: true },
    }),
    trackingNumber: text({
      validation: { isRequired: true },
    }),
    purchaseId: text(),

    // Relationships
    cartItems: relationship({
      ref: "CartItem.trackingDetails",
      many: true,
      ui: {
        displayMode: "cards",
        cardFields: ["name", "quantity", "status"],
      },
    }),
    user: relationship({
      ref: "User.trackingDetails",
    }),

    ...trackingFields,
  },
});
