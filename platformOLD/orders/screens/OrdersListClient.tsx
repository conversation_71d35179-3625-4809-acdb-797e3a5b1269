'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { getListByPath } from "@/features/dashboard/actions";
import { getFilteredOrders, getOrderStatusCounts, getChannels, deleteManyOrders, placeOrders } from "@/features/platform/orders/actions";
import { PageBreadcrumbs } from "@/features/dashboard/components/PageBreadcrumbs";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Circle, Square, Triangle } from "lucide-react";
import { StatusTabs } from "../components/StatusTabs";
import { PlatformFilterBar } from '@/features/dashboard/components/PlatformFilterBar';
import type { SortOption } from '@/features/dashboard/components/PlatformFilterBar';
import { CreateOrderButton } from "../components/CreateOrderButton";
import { OrderPageClient } from './OrderPageClient';
import { Pagination } from '@/features/dashboard/components/Pagination';
import { useToast } from '@/components/ui/use-toast';
import { ProcessOrdersDialog } from '../components/ProcessOrdersDialog';
import { Order } from '../lib/types';

interface OrdersListClientProps {
  orders: Order[];
  count: number;
  list: any;
  statusCounts: any;
  channels: any[];
  searchParams: { [key: string]: string | string[] | undefined };
}

export function OrdersListClient({
  orders,
  count,
  list,
  statusCounts,
  channels,
  searchParams,
}: OrdersListClientProps) {
  const [selectedOrders, setSelectedOrders] = useState<Set<string>>(new Set());
  const [isDeleteLoading, setIsDeleteLoading] = useState(false);
  const [isProcessOrdersDialogOpen, setIsProcessOrdersDialogOpen] = useState(false);
  const [processingOrders, setProcessingOrders] = useState<string[]>([]);

  const { toast } = useToast();
  const router = useRouter();
  const page = Number(searchParams.page) || 1;
  const pageSize = Number(searchParams.pageSize) || 10;
  const statusFilter = searchParams["!status_matches"];
  const search = typeof searchParams.search === "string" && searchParams.search !== "" ? searchParams.search : null;
  const sortBy = searchParams.sortBy as string | undefined;

  const handleDelete = async () => {
    const idsToDelete = Array.from(selectedOrders);
    if (idsToDelete.length === 0) return;

    setIsDeleteLoading(true);
    try {
      const response = await deleteManyOrders(idsToDelete);

      if (response.success) {
        setSelectedOrders(new Set());
        toast({
          title: 'Success',
          description: `Successfully deleted ${idsToDelete.length} ${
            idsToDelete.length === 1 ? 'order' : 'orders'
          }`,
        });
        router.refresh();
      } else {
        console.error("Error deleting orders:", response.error);
        toast({
          title: "Failed to delete orders",
          description: response.error,
          variant: "destructive",
        });
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'An unexpected error occurred';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsDeleteLoading(false);
    }
  };

  const handleProcessOrders = async (orderIds: string[]) => {
    setProcessingOrders(orderIds);
    const response = await placeOrders(orderIds);
    if (response.success) {
        toast({ title: 'Success', description: 'Orders sent for processing.' });
        setSelectedOrders(new Set());
        router.refresh();
    } else {
        toast({ title: 'Action Failed', description: response.error, variant: 'destructive' });
    }
    setProcessingOrders([]);
    setIsProcessOrdersDialogOpen(false);
  };

  const sort = sortBy ? {
    field: sortBy.startsWith("-") ? sortBy.slice(1) : sortBy,
    direction: sortBy.startsWith("-") ? "DESC" : "ASC"
  } as SortOption : null;

  const handleSelectAll = (checked: boolean) => {
    const newSelectedOrders = new Set<string>();
    if (checked) {
      orders.forEach(order => newSelectedOrders.add(order.id));
    }
    setSelectedOrders(newSelectedOrders);
  };

  return (
    <section
      aria-label="Orders overview"
      className="overflow-hidden flex flex-col"
    >
      <PageBreadcrumbs
        items={[
          {
            type: "link",
            label: "Dashboard",
            href: "/",
          },
          {
            type: "page",
            label: "Platform",
          },
          {
            type: "page",
            label: "Orders",
          },
        ]}
      />

      <div className="flex flex-col flex-1 min-h-0">

        <div className="border-gray-200 dark:border-gray-800">
          <div className="px-4 md:px-6 pt-4 md:pt-6 pb-4">
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-50">
              Orders
            </h1>
            <p className="text-muted-foreground">
              <span>Create and manage orders</span>
            </p>
          </div>
        </div>

        <PlatformFilterBar
          list={list as any}
          currentSort={sort}
          createButtonElement={<CreateOrderButton />}
        />

        <div className="border-b">
          <StatusTabs
            statusCounts={statusCounts}
            onSelectAll={handleSelectAll}
            selectedItems={selectedOrders}
            totalItems={orders.length}
          />
        </div>

        <div className="flex-1 overflow-auto">
          {orders && orders.length > 0 ? (
            <OrderPageClient
              orders={orders}
              channels={channels}
              selectedOrders={selectedOrders}
              onSelectedOrdersChange={setSelectedOrders}
            />
          ) : (
            <div className="flex items-center justify-center h-full py-10">
              <div className="text-center">
                <div className="relative h-11 w-11 mx-auto mb-2">
                  <Triangle className="absolute left-1 top-1 w-4 h-4 fill-indigo-200 stroke-indigo-400 dark:stroke-indigo-600 dark:fill-indigo-950 rotate-[90deg]" />
                  <Square className="absolute right-[.2rem] top-1 w-4 h-4 fill-orange-300 stroke-orange-500 dark:stroke-amber-600 dark:fill-amber-950 rotate-[30deg]" />
                  <Circle className="absolute bottom-2 left-1/2 -translate-x-1/2 w-4 h-4 fill-emerald-200 stroke-emerald-400 dark:stroke-emerald-600 dark:fill-emerald-900" />
                </div>
                <p className="font-medium">No orders found</p>
                <p className="text-muted-foreground text-sm">
                  {(search !== null) || statusFilter
                    ? "Try adjusting your search or filter criteria"
                    : "Create your first order to get started"}
                </p>
                {((search !== null) || statusFilter) && (
                  <Link href="/dashboard/platform/orders">
                    <Button variant="outline" className="mt-4" size="sm">
                      Clear filters
                    </Button>
                  </Link>
                )}
              </div>
            </div>
          )}
        </div>

        <Pagination
          currentPage={page}
          total={count}
          pageSize={pageSize}
          list={{
            singular: 'Order',
            plural: 'Orders',
          }}
          selectedItems={selectedOrders}
          onResetSelection={() => setSelectedOrders(new Set())}
          onDelete={handleDelete}
          isDeleteLoading={isDeleteLoading}
          renderActions={(selectedItems) => (
            <Button
              onClick={() => setIsProcessOrdersDialogOpen(true)}
              variant="default"
              className="font-semibold rounded-md gap-3"
            >
              <span className="truncate uppercase tracking-wide">
                Process {selectedItems.size}{" "}
                {selectedItems.size === 1 ? 'Order' : 'Orders'}
              </span>
            </Button>
          )}
        />
      </div>
      <ProcessOrdersDialog
        isOpen={isProcessOrdersDialogOpen}
        onClose={() => {
          setIsProcessOrdersDialogOpen(false);
          setProcessingOrders([]);
        }}
        orders={orders.filter(o => selectedOrders.has(o.id) || processingOrders.includes(o.id))}
        onProcessOrders={() => handleProcessOrders(Array.from(selectedOrders.size > 0 ? selectedOrders : processingOrders))}
        processingOrders={processingOrders}
        channels={channels}
        onAction={() => {}}
      />
    </section>
  );
}