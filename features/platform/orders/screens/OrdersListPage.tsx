import { getListByPath } from "@/features/dashboard/actions";
import { getFilteredOrders, getOrderStatusCounts, getChannels } from "@/features/platform/orders/actions";
import { OrdersListClient } from './OrdersListClient';
import { Order } from '../lib/types';

interface PageProps {
  searchParams: { [key: string]: string | string[] | undefined };
}

function ErrorDisplay({ title, message }: { title: string; message: string }) {
  return (
    <div className="px-4 sm:px-6 lg:px-8 py-8">
      <h1 className="text-2xl font-bold tracking-tight text-red-600">
        {title}
      </h1>
      <p className="mt-2 text-gray-600">{message}</p>
    </div>
  );
}

export async function OrdersListPage({ searchParams }: PageProps) {
  const page = Number(searchParams.page) || 1;
  const pageSize = Number(searchParams.pageSize) || 10;

  let status = null;
  const statusFilter = searchParams["!status_matches"];
  if (statusFilter) {
    try {
      const parsed = JSON.parse(decodeURIComponent(statusFilter as string));
      if (Array.isArray(parsed) && parsed.length > 0) {
        status = parsed[0].value;
      }
    } catch (e) {
      // Invalid JSON in URL, ignore
    }
  }

  const search = typeof searchParams.search === "string" && searchParams.search !== "" ? searchParams.search : null;

  const [list, ordersData, statusCountsData, channelsData] = await Promise.all([
    getListByPath("orders"),
    getFilteredOrders(
      status,
      search,
      page,
      pageSize,
      (() => {
        const sortBy = searchParams.sortBy as string | undefined;
        return sortBy ? {
          field: sortBy.startsWith("-") ? sortBy.slice(1) : sortBy,
          direction: sortBy.startsWith("-") ? "DESC" : "ASC"
        } : null;
      })()
    ),
    getOrderStatusCounts(),
    getChannels(),
  ]);

  if (!list) {
    return (
      <ErrorDisplay
        title="Invalid List"
        message="The requested list could not be found."
      />
    );
  }

  const orders: Order[] = ordersData.success ? ordersData.data?.items || [] : [];
  const count = ordersData.success ? ordersData.data?.count || 0 : 0;
  const statusCounts = statusCountsData.success ? statusCountsData.data : {};
  const channels = channelsData || [];

  return (
    <OrdersListClient
      orders={orders}
      count={count}
      list={list}
      statusCounts={statusCounts}
      channels={channels}
      searchParams={searchParams}
    />
  );
}
