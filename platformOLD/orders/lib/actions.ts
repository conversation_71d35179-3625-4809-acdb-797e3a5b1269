'use server';

import { revalidatePath } from 'next/cache';
import { keystoneClient } from '@/features/dashboard/lib/keystoneClient';

export async function getCurrentUser() {
  const query = `
    query GetCurrentUser {
      authenticatedItem {
        ... on User {
          id
          name
          email
        }
      }
    }
  `;

  const response = await keystoneClient(query);
  return response;
}

export async function getOrder(orderId: string) {
  const query = `
    query ($id: ID!) {
      order(where: { id: $id }) {
        id
        orderId
        orderName
        email
        first_name
        last_name
        streetAddress1
        streetAddress2
        city
        state
        zip
        country
        phone
        currency
        totalPrice
        subTotalPrice
        totalDiscounts
        totalTax
        linkOrder
        matchOrder
        processOrder
        status
        error
        orderMetadata
        createdAt
        updatedAt
        user {
          id
          name
          email
          phone
        }
        shop {
          id
          name
          domain
          accessToken
        }
        lineItems {
          id
          name
          image
          price
          quantity
          productId
          variantId
          sku
          lineItemId
        }
        cartItems {
          id
          name
          image
          price
          quantity
          productId
          variantId
          sku
        }
      }
    }
  `;

  const cacheOptions = {
    next: {
      tags: [`order-${orderId}`],
      revalidate: 3600, // Cache for 1 hour
    },
  };

  const response = await keystoneClient(query, { id: orderId }, cacheOptions);
  return response;
}