"use client";

import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON>le, Di<PERSON>Trigger } from "@/components/ui/dialog";
import { Plus, Pencil } from "lucide-react";
import { cn } from "@/lib/utils";

interface Platform {
  id: string;
  name: string;
  [key: string]: any;
}

interface PlatformCardProps {
  platforms: Platform[];
  selectedPlatform?: string | null;
  onPlatformSelect: (platformId: string | null) => void;
  onPlatformCreate?: () => void;
  onPlatformEdit?: (platformId: string) => void;
  platformType: "shop" | "channel";
}

export function PlatformCard({
  platforms,
  selectedPlatform,
  onPlatformSelect,
  onPlatformCreate,
  onPlatformEdit,
  platformType,
}: PlatformCardProps) {
  return (
    <Card className="mb-6">
      <div className="p-4">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium text-muted-foreground">
            {platformType === "shop" ? "Shop Platforms" : "Channel Platforms"}
          </h3>
          {onPlatformCreate && (
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={onPlatformCreate}
            >
              <Plus className="h-4 w-4" />
            </Button>
          )}
        </div>
        
        <div className="flex flex-wrap gap-2">
          {platforms.length === 0 ? (
            <p className="text-sm text-muted-foreground">
              No platforms configured yet.
            </p>
          ) : (
            <>
              <Badge
                variant={selectedPlatform === null ? "default" : "outline"}
                className={cn(
                  "cursor-pointer transition-colors",
                  selectedPlatform === null && "bg-primary text-primary-foreground"
                )}
                onClick={() => onPlatformSelect(null)}
              >
                All
              </Badge>
              
              {platforms.map((platform) => (
                <div key={platform.id} className="relative group">
                  <Badge
                    variant={selectedPlatform === platform.id ? "default" : "outline"}
                    className={cn(
                      "cursor-pointer transition-colors pr-8",
                      selectedPlatform === platform.id && "bg-primary text-primary-foreground"
                    )}
                    onClick={() => onPlatformSelect(platform.id)}
                  >
                    {platform.name}
                  </Badge>
                  
                  {onPlatformEdit && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="absolute right-0 top-0 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={(e) => {
                        e.stopPropagation();
                        onPlatformEdit(platform.id);
                      }}
                    >
                      <Pencil className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              ))}
            </>
          )}
        </div>
      </div>
    </Card>
  );
}