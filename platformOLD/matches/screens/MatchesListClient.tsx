'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { PageBreadcrumbs } from "@/features/dashboard/components/PageBreadcrumbs";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Circle, Square, Triangle } from "lucide-react";
import { StatusTabs } from "../components/StatusTabs";
import { PlatformFilterBar } from '@/features/dashboard/components/PlatformFilterBar';
import type { SortOption } from '@/features/dashboard/components/PlatformFilterBar';
import { CreateMatchButton } from "../components/CreateMatchButton";
import { MatchDetailsDialog } from "../components/MatchDetailsDialog";
import { MatchPageClient } from './MatchPageClient';
import { Pagination } from '@/features/dashboard/components/Pagination';
import { useToast } from '@/components/ui/use-toast';
import { ShopProductsSearch } from "../components/ShopProductsSearch";
import { ChannelProductsSearch } from "../components/ChannelProductsSearch";

interface Match {
  id: string;
  outputPriceChanged?: string;
  inventoryNeedsToBeSynced?: string;
  input?: any[];
  output?: any[];
  user?: any;
  createdAt: string;
  updatedAt?: string;
}

interface MatchesListClientProps {
  matches: Match[];
  count: number;
  list: any;
  statusCounts: {
    shop: number;
    channel: number;
    matches: number;
  };
  shops: any[];
  channels: any[];
  searchParams: { [key: string]: string | string[] | undefined };
}

export function MatchesListClient({
  matches,
  count,
  list,
  statusCounts,
  shops,
  channels,
  searchParams,
}: MatchesListClientProps) {
  const [selectedMatches, setSelectedMatches] = useState<Set<string>>(new Set());
  const [isDeleteLoading, setIsDeleteLoading] = useState(false);
  const [isCreateMatchDialogOpen, setIsCreateMatchDialogOpen] = useState(false);

  const { toast } = useToast();
  const router = useRouter();
  const page = Number(searchParams.page) || 1;
  const pageSize = Number(searchParams.pageSize) || 10;
  const statusFilter = searchParams["!status_matches"];
  const search = typeof searchParams.search === "string" && searchParams.search !== "" ? searchParams.search : null;
  const sortBy = searchParams.sortBy as string | undefined;
  
  // Get current tab from status filter
  let currentTab = "SHOP";
  if (statusFilter) {
    try {
      const parsed = JSON.parse(decodeURIComponent(statusFilter as string));
      if (Array.isArray(parsed) && parsed.length > 0) {
        currentTab = parsed[0].value;
      }
    } catch (e) {
      // Invalid JSON in URL, ignore
    }
  }

  const handleDelete = async () => {
    const idsToDelete = Array.from(selectedMatches);
    if (idsToDelete.length === 0) return;

    setIsDeleteLoading(true);
    try {
      // TODO: Implement deleteMatches action
      console.log("Delete matches:", idsToDelete);
      
      setSelectedMatches(new Set());
      toast({
        title: 'Success',
        description: `Successfully deleted ${idsToDelete.length} ${
          idsToDelete.length === 1 ? 'match' : 'matches'
        }`,
      });
      router.refresh();
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'An unexpected error occurred';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsDeleteLoading(false);
    }
  };

  const sort = sortBy ? {
    field: sortBy.startsWith("-") ? sortBy.slice(1) : sortBy,
    direction: sortBy.startsWith("-") ? "DESC" : "ASC"
  } as SortOption : null;

  const handleSelectAll = (checked: boolean) => {
    const newSelectedMatches = new Set<string>();
    if (checked) {
      matches.forEach(match => newSelectedMatches.add(match.id));
    }
    setSelectedMatches(newSelectedMatches);
  };

  return (
    <section
      aria-label="Matches overview"
      className="overflow-hidden flex flex-col"
    >
      <PageBreadcrumbs
        items={[
          {
            type: "link",
            label: "Dashboard",
            href: "/",
          },
          {
            type: "page",
            label: "Platform",
          },
          {
            type: "page",
            label: "Matches",
          },
        ]}
      />

      <div className="flex flex-col flex-1 min-h-0">

        <div className="border-gray-200 dark:border-gray-800">
          <div className="px-4 md:px-6 pt-4 md:pt-6 pb-4">
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-50">
              Matches
            </h1>
            <p className="text-muted-foreground">
              <span>Manage matches across shops and channels</span>
            </p>
          </div>
        </div>

        <PlatformFilterBar
          list={list as any}
          currentSort={sort}
          createButtonElement={<CreateMatchButton onClick={() => setIsCreateMatchDialogOpen(true)} />}
        />

        <div className="border-b">
          <StatusTabs
            statusCounts={statusCounts}
            onSelectAll={handleSelectAll}
            selectedItems={selectedMatches}
            totalItems={matches.length}
          />
        </div>

        <div className="flex-1 overflow-auto">
          {currentTab === "SHOP" && (
            <ShopProductsSearch shops={shops} />
          )}
          
          {currentTab === "CHANNEL" && (
            <ChannelProductsSearch channels={channels} />
          )}
          
          {currentTab === "MATCHES" && (
            matches && matches.length > 0 ? (
              <MatchPageClient
                matches={matches}
                shops={shops}
                channels={channels}
                selectedMatches={selectedMatches}
                onSelectedMatchesChange={setSelectedMatches}
              />
            ) : (
              <div className="flex items-center justify-center h-full py-10">
                <div className="text-center">
                  <div className="relative h-11 w-11 mx-auto mb-2">
                    <Triangle className="absolute left-1 top-1 w-4 h-4 fill-indigo-200 stroke-indigo-400 dark:stroke-indigo-600 dark:fill-indigo-950 rotate-[90deg]" />
                    <Square className="absolute right-[.2rem] top-1 w-4 h-4 fill-orange-300 stroke-orange-500 dark:stroke-amber-600 dark:fill-amber-950 rotate-[30deg]" />
                    <Circle className="absolute bottom-2 left-1/2 -translate-x-1/2 w-4 h-4 fill-emerald-200 stroke-emerald-400 dark:stroke-emerald-600 dark:fill-emerald-900" />
                  </div>
                  <p className="font-medium">No matches found</p>
                  <p className="text-muted-foreground text-sm">
                    {(search !== null) || statusFilter
                      ? "Try adjusting your search or filter criteria"
                      : "Create your first match to get started"}
                  </p>
                  {((search !== null) || statusFilter) && (
                    <Link href="/dashboard/platform/matches">
                      <Button variant="outline" className="mt-4" size="sm">
                        Clear filters
                      </Button>
                    </Link>
                  )}
                </div>
              </div>
            )
          )}
        </div>

        {currentTab === "MATCHES" && (
          <Pagination
            currentPage={page}
            total={count}
            pageSize={pageSize}
            list={{
              singular: 'Match',
              plural: 'Matches',
            }}
            selectedItems={selectedMatches}
            onResetSelection={() => setSelectedMatches(new Set())}
            onDelete={handleDelete}
            isDeleteLoading={isDeleteLoading}
            renderActions={(selectedItems) => (
              <Button
                variant="default"
                className="font-semibold rounded-md gap-3"
              >
                <span className="truncate uppercase tracking-wide">
                  Sync {selectedItems.size}{" "}
                  {selectedItems.size === 1 ? 'Match' : 'Matches'}
                </span>
              </Button>
            )}
          />
        )}
        
        <MatchDetailsDialog
          open={isCreateMatchDialogOpen}
          onClose={() => setIsCreateMatchDialogOpen(false)}
          shops={shops}
          channels={channels}
        />
      </div>
    </section>
  );
}