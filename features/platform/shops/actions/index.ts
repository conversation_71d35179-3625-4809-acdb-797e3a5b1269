'use server';

import { revalidatePath } from 'next/cache';
import { keystoneClient } from "../../../dashboard/lib/keystoneClient";

// Interface for shop data (exported for potential use in other files)
export interface Shop {
  id: string;
  title: string;
  [key: string]: unknown;
}

/**
 * Get list of shops
 */
export async function getShops(
  where: Record<string, unknown> = {},
  take: number = 10,
  skip: number = 0,
  orderBy: Array<Record<string, string>> = [{ createdAt: 'desc' }],
  selectedFields: string = `
    id name domain accessToken linkMode metadata createdAt updatedAt platform { id name } user { id name email } orders { id } shopItems { id } links { id }
  `
) {
  const query = `
    query GetShops($where: ShopWhereInput, $take: Int!, $skip: Int!, $orderBy: [ShopOrderByInput!]) {
      items: shops(where: $where, take: $take, skip: $skip, orderBy: $orderBy) {
        ${selectedFields}
      }
      count: shopsCount(where: $where)
    }
  `;

  const response = await keystoneClient(query, {
    where,
    take,
    skip,
    orderBy,
  });

  if (response.success) {
    return {
      success: true,
      data: {
        items: response.data.items || [],
        count: response.data.count || 0,
      },
    };
  } else {
    console.error('Error fetching shops:', response.error);
    return {
      success: false,
      error: response.error || 'Failed to fetch shops',
      data: { items: [], count: 0 },
    };
  }
}

/**
 * Get filtered shops with search and pagination
 */
export async function getFilteredShops(
  search?: string,
  page: number = 1,
  pageSize: number = 10,
  sort?: string
) {
  // Build where clause
  const where: Record<string, any> = {};

  // Search filtering (adjust fields as needed)
  if (search?.trim()) {
    where.OR = [
      { name: { contains: search, mode: 'insensitive' } },
      { domain: { contains: search, mode: 'insensitive' } },
      // Add more searchable fields as needed
    ];
  }

  // Build orderBy clause
  let orderBy: Array<Record<string, string>> = [{ createdAt: 'desc' }];
  if (sort) {
    if (sort.startsWith('-')) {
      const field = sort.substring(1);
      orderBy = [{ [field]: 'desc' }];
    } else {
      orderBy = [{ [sort]: 'asc' }];
    }
  }

  // Calculate pagination
  const skip = (page - 1) * pageSize;

  try {
    const result = await getShops(where, pageSize, skip, orderBy);
    return result;
  } catch (error: any) {
    console.error('Error in getFilteredShops:', error);
    return {
      success: false,
      error: error.message || 'Failed to get filtered shops',
      data: { items: [], count: 0 },
    };
  }
}

/**
 * Get a single shop by ID
 */
export async function getShop(id: string) {
  const query = `
    query GetShop($id: ID!) {
      shop(where: { id: $id }) {
        id name domain accessToken linkMode metadata createdAt updatedAt platform { id name } user { id name email } orders { id } shopItems { id } links { id }
      }
    }
  `;

  const response = await keystoneClient(query, { id });

  if (response.success) {
    if (!response.data.shop) {
      return {
        success: false,
        error: 'Shop not found',
        data: null,
      };
    }

    return {
      success: true,
      data: response.data.shop,
    };
  } else {
    console.error('Error fetching shop:', response.error);
    return {
      success: false,
      error: response.error || 'Failed to fetch shop',
      data: null,
    };
  }
}



/**
 * Get list of shop platforms
 */
export async function getShopPlatforms() {
  const query = `
    query GetShopPlatforms {
      shopPlatforms {
        id
        name
        appKey
        appSecret
        oAuthFunction
        oAuthCallbackFunction
        createdAt
        updatedAt
        shops {
          id
        }
      }
    }
  `;

  const response = await keystoneClient(query, {});
  return response;
}

/**
 * Get filtered shops with platform filter
 */
export async function getFilteredShopsWithPlatform(
  search: string | null = null,
  platformId: string | null = null,
  page: number = 1,
  pageSize: number = 10,
  sort: { field: string; direction: 'ASC' | 'DESC' } | null = null
) {
  const where: Record<string, unknown> = {};

  // Add platform filter if provided
  if (platformId) {
    where.platform = { id: { equals: platformId } };
  }

  // Add search filter if provided
  if (search) {
    const searchConditions = [
      { name: { contains: search, mode: 'insensitive' } },
      { domain: { contains: search, mode: 'insensitive' } },
      { user: { name: { contains: search, mode: 'insensitive' } } },
      { user: { email: { contains: search, mode: 'insensitive' } } },
    ];

    if (platformId) {
      // If platform filter is active, combine it with search
      where.AND = [
        { platform: { id: { equals: platformId } } },
        { OR: searchConditions }
      ];
    } else {
      // If no platform filter, include platform name in search
      searchConditions.push({ platform: { name: { contains: search, mode: 'insensitive' } } });
      where.OR = searchConditions;
    }
  }

  // Calculate pagination
  const skip = (page - 1) * pageSize;

  // Handle sorting
  const orderBy = sort
    ? [{ [sort.field]: sort.direction.toLowerCase() }]
    : [{ createdAt: 'desc' }];

  return getShops(where, pageSize, skip, orderBy);
}

/**
 * Create shop platform
 */
export async function createShopPlatform(data: Record<string, unknown>) {
  const query = `
    mutation CreateShopPlatform($data: ShopPlatformCreateInput!) {
      createShopPlatform(data: $data) {
        id
        name
      }
    }
  `;

  const response = await keystoneClient(query, { data });

  if (response.success) {
    revalidatePath('/dashboard/platform/shops');
  }

  return response;
}

/**
 * Update shop platform
 */
export async function updateShopPlatform(id: string, data: Record<string, unknown>) {
  const query = `
    mutation UpdateShopPlatform($id: ID!, $data: ShopPlatformUpdateInput!) {
      updateShopPlatform(where: { id: $id }, data: $data) {
        id
        name
      }
    }
  `;

  const response = await keystoneClient(query, { id, data });

  if (response.success) {
    revalidatePath('/dashboard/platform/shops');
  }

  return response;
}

/**
 * Create shop
 */
export async function createShop(data: {
  name: string;
  domain?: string;
  accessToken?: string;
  platformId: string;
  linkMode?: string;
  metadata?: any;
}) {
  const query = `
    mutation CreateShop($data: ShopCreateInput!) {
      createShop(data: $data) {
        id
        name
        domain
        platform {
          id
          name
        }
        linkMode
        createdAt
      }
    }
  `;

  const shopData = {
    name: data.name,
    domain: data.domain,
    accessToken: data.accessToken,
    platform: { connect: { id: data.platformId } },
    linkMode: data.linkMode || "sequential",
    metadata: data.metadata || {},
  };

  const response = await keystoneClient(query, { data: shopData });

  if (response.success) {
    revalidatePath('/dashboard/platform/shops');
  }

  return response;
}
