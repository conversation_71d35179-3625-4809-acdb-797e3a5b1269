import { NextRequest, NextResponse } from 'next/server';
import { keystoneContext } from '@/features/keystone/context';
import { handleChannelTrackingWebhook } from '@/features/integrations/channel/lib/executor';

export async function POST(
  request: NextRequest,
  { params }: { params: { channelId: string } }
) {
  try {
    // Respond immediately to acknowledge receipt
    const response = NextResponse.json({ received: true });

    // Get the webhook payload
    const body = await request.json();
    const headers = Object.fromEntries(request.headers.entries());
    const { channelId } = params;

    // Process webhook asynchronously
    processWebhook(channelId, body, headers);

    return response;
  } catch (error) {
    console.error('Error processing create tracking webhook:', error);
    return NextResponse.json({ error: 'Webhook processing failed' }, { status: 500 });
  }
}

async function processWebhook(channelId: string, body: any, headers: any) {
  try {
    // Find the channel and its platform
    const channel = await keystoneContext.sudo().query.Channel.findOne({
      where: { id: channelId },
      query: `
        id
        domain
        accessToken
        user {
          id
          email
        }
        platform {
          id
          name
          createTrackingWebhookHandler
          appKey
          appSecret
        }
      `,
    });

    if (!channel) {
      console.error(`Channel not found: ${channelId}`);
      return;
    }

    console.log('Processing tracking webhook for channel:', channel.domain);

    // Use the channel provider adapter to handle the webhook
    const trackingData = await handleChannelTrackingWebhook({
      platform: {
        ...channel.platform,
        domain: channel.domain,
        accessToken: channel.accessToken,
      },
      event: body,
      headers,
    });

    console.log('Tracking data received from adapter:', trackingData);

    const { purchaseId, trackingNumber, trackingCompany } = trackingData;

    // Find the cart items associated with this purchase
    const cartItems = await keystoneContext.sudo().query.CartItem.findMany({
      where: { purchaseId: { equals: purchaseId } },
      query: `
        id
        purchaseId
        quantity
        price
        productId
        variantId
        title
        image
        order {
          id
          orderName
        }
      `,
    });

    if (cartItems.length === 0) {
      console.warn(`No cart items found for purchaseId: ${purchaseId}`);
      return;
    }

    // Create tracking detail record
    const trackingDetail = await keystoneContext.sudo().query.TrackingDetail.createOne({
      data: {
        trackingNumber,
        trackingCompany,
        cartItems: {
          connect: cartItems.map(item => ({ id: item.id })),
        },
      },
      query: `
        id
        trackingNumber
        trackingCompany
        cartItems {
          id
          title
          order {
            id
            orderName
          }
        }
      `,
    });

    console.log('Tracking detail created successfully:', trackingDetail);
  } catch (error) {
    console.error('Error processing tracking webhook:', error);
  }
}