'use client';

import React, { useState, useEffect } from 'react';
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, Plus, Loader2, Search } from 'lucide-react';
import { searchShopProducts } from '../actions/matches';
import { useToast } from '@/components/ui/use-toast';

interface Shop {
  id: string;
  name: string;
  domain?: string;
}

interface Product {
  image?: string;
  title: string;
  productId: string;
  variantId: string;
  price?: string;
  availableForSale?: boolean;
  inventory?: number;
  inventoryTracked?: boolean;
}

interface SelectedLineItem {
  quantity: number;
  productId: string;
  variantId: string;
  shop: {
    id: string;
    name: string;
  };
  price?: string;
  title?: string;
  image?: string;
}

interface LineItemSelectProps {
  orderId?: string;
  selectedItems: SelectedLineItem[];
  onSelectionChange: (items: SelectedLineItem[]) => void;
  shops: Shop[];
}

export const LineItemSelect: React.FC<LineItemSelectProps> = ({
  orderId,
  selectedItems,
  onSelectionChange,
  shops,
}) => {
  const [selectedShopId, setSelectedShopId] = useState(shops[0]?.id || '');
  const [searchEntry, setSearchEntry] = useState('');
  const [searchResults, setSearchResults] = useState<Product[]>([]);
  const [quantities, setQuantities] = useState<Record<string, number>>({});
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  // Update selected shop when shops change
  useEffect(() => {
    if (!selectedShopId && shops.length > 0) {
      setSelectedShopId(shops[0].id);
    }
  }, [shops, selectedShopId]);

  const handleSearch = async () => {
    if (!selectedShopId || !searchEntry) {
      toast({
        title: 'Missing Information',
        description: 'Please select a shop and enter a search term.',
        variant: 'destructive',
      });
      return;
    }

    setLoading(true);
    try {
      const response = await searchShopProducts(selectedShopId, searchEntry);
      
      if (response.error) {
        throw new Error(response.error);
      }
      
      setSearchResults(response.searchShopProducts?.products || []);
    } catch (error: any) {
      toast({
        title: 'Search Failed',
        description: error.message,
        variant: 'destructive',
      });
      setSearchResults([]);
    } finally {
      setLoading(false);
    }
  };

  const getQuantity = (productId: string) => {
    const key = `${selectedShopId}-${productId}`;
    return quantities[key] || 1;
  };

  const setQuantity = (productId: string, quantity: number) => {
    const key = `${selectedShopId}-${productId}`;
    setQuantities(prev => ({ ...prev, [key]: Math.max(1, quantity) }));
  };

  const handleAddItem = (product: Product) => {
    const selectedShop = shops.find(shop => shop.id === selectedShopId);
    if (!selectedShop) return;

    const quantity = getQuantity(product.productId);
    const newItem: SelectedLineItem = {
      quantity,
      productId: product.productId,
      variantId: product.variantId,
      shop: {
        id: selectedShop.id,
        name: selectedShop.name,
      },
      price: product.price,
      title: product.title,
      image: product.image,
    };

    onSelectionChange([...selectedItems, newItem]);
    
    // Reset quantity for this product
    setQuantity(product.productId, 1);
    
    toast({
      title: 'Item Added',
      description: `${product.title} added to selection`,
    });
  };

  const removeSelectedItem = (index: number) => {
    const newItems = [...selectedItems];
    newItems.splice(index, 1);
    onSelectionChange(newItems);
  };

  const calculateTotal = (item: SelectedLineItem) => {
    if (!item.price) return null;
    const total = parseFloat(item.price) * item.quantity;
    return total.toFixed(2);
  };

  return (
    <div className="flex flex-col h-full gap-4">
      {/* Search Controls */}
      <div className="flex gap-2">
        <Select value={selectedShopId} onValueChange={setSelectedShopId} disabled={shops.length === 0}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder={shops.length === 0 ? "No shops available" : "Select a shop"} />
          </SelectTrigger>
          <SelectContent>
            {shops.map((shop) => (
              <SelectItem key={shop.id} value={shop.id}>
                {shop.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Input
          placeholder="Search shop products..."
          value={searchEntry}
          onChange={(e) => setSearchEntry(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
          className="flex-1"
        />
        <Button onClick={handleSearch} disabled={loading || !selectedShopId || !searchEntry}>
          {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Search className="h-4 w-4" />}
        </Button>
      </div>

      {/* Selected Items */}
      {selectedItems.length > 0 && (
        <div className="border rounded-lg p-3 bg-blue-50/30 dark:bg-blue-900/10">
          <h4 className="text-sm font-medium mb-2">Selected Shop Products ({selectedItems.length})</h4>
          <div className="space-y-2 max-h-32 overflow-y-auto">
            {selectedItems.map((item, index) => (
              <div key={`${item.shop.id}-${item.productId}-${index}`} className="flex items-center justify-between text-sm bg-white dark:bg-gray-800 p-2 rounded">
                <div className="flex items-center gap-2">
                  {item.image && (
                    <img src={item.image} alt={item.title} className="w-8 h-8 rounded object-cover" />
                  )}
                  <div>
                    <div className="font-medium">{item.title}</div>
                    <div className="text-xs text-muted-foreground">
                      {item.shop.name} • Qty: {item.quantity}
                      {item.price && ` • $${calculateTotal(item)}`}
                    </div>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => removeSelectedItem(index)}
                  className="h-6 w-6 p-0"
                >
                  ×
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Search Results */}
      <div className="flex-1 overflow-y-auto">
        {searchResults.length > 0 ? (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Search Results ({searchResults.length})</h4>
            {searchResults.map((product) => (
              <div
                key={`${product.productId}-${product.variantId}`}
                className="flex items-center justify-between p-3 bg-background rounded-lg border"
              >
                <div className="flex items-center gap-3 flex-1">
                  {product.image && (
                    <div className="w-12 h-12 flex-shrink-0 rounded-md overflow-hidden">
                      <img
                        src={product.image}
                        alt={product.title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  )}
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium truncate">{product.title}</div>
                    <div className="text-xs text-muted-foreground truncate">
                      {product.productId} | {product.variantId}
                    </div>
                    {product.price && (
                      <div className="text-sm font-medium text-green-600">
                        ${parseFloat(product.price).toFixed(2)}
                      </div>
                    )}
                    {product.inventoryTracked && (
                      <div className="text-xs text-muted-foreground">
                        Stock: {product.inventory || 0}
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center gap-2 flex-shrink-0">
                  <div className="flex items-center gap-1">
                    <Button
                      variant="outline"
                      size="icon"
                      className="h-6 w-6"
                      onClick={() => setQuantity(product.productId, getQuantity(product.productId) - 1)}
                      disabled={getQuantity(product.productId) <= 1}
                    >
                      <ChevronLeft className="h-3 w-3" />
                    </Button>
                    <Input
                      className="w-12 h-6 text-center text-xs"
                      type="number"
                      min="1"
                      value={getQuantity(product.productId)}
                      onChange={(e) => setQuantity(product.productId, parseInt(e.target.value) || 1)}
                    />
                    <Button
                      variant="outline"
                      size="icon"
                      className="h-6 w-6"
                      onClick={() => setQuantity(product.productId, getQuantity(product.productId) + 1)}
                    >
                      <ChevronRight className="h-3 w-3" />
                    </Button>
                  </div>
                  
                  {product.price && (
                    <div className="text-xs text-muted-foreground min-w-[60px] text-right">
                      ${(parseFloat(product.price) * getQuantity(product.productId)).toFixed(2)}
                    </div>
                  )}
                  
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-6 w-6"
                    onClick={() => handleAddItem(product)}
                    disabled={!product.availableForSale}
                  >
                    <Plus className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        ) : searchEntry && !loading ? (
          <div className="text-center text-muted-foreground py-8">
            No products found for "{searchEntry}"
          </div>
        ) : (
          <div className="text-center text-muted-foreground py-8">
            {selectedShopId ? 'Enter a search term to find products' : 'Select a shop to start searching'}
          </div>
        )}
      </div>
    </div>
  );
};