"use client";

import React, { useState } from "react";
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from "@/components/ui/accordion";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { MoreVertical, Package } from "lucide-react";
import Link from "next/link";
import { EditItemDrawer } from "@/features/dashboard/components/EditItemDrawer";
import { format } from "date-fns";

interface Collection {
  id: string;
  title: string;
  handle: string;
  metadata?: any;
  createdAt: string;
  updatedAt: string;
  productsCount?: number;
  products?: Array<{
    id: string;
    title: string;
    handle?: string;
    status: string;
    thumbnail?: string;
    productType?: {
      id: string;
      value: string;
    };
    productVariants?: Array<{
      id: string;
      title: string;
      sku?: string;
      inventoryQuantity?: number;
    }>;
  }>;
}

interface CollectionDetailsComponentProps {
  collection: Collection;
}

export function CollectionDetailsComponent({
  collection,
}: CollectionDetailsComponentProps) {
  const [isEditDrawerOpen, setIsEditDrawerOpen] = useState(false);

  const productsCount = collection.productsCount || 0;
  const products = collection.products || [];

  return (
    <>
      <Accordion type="single" collapsible className="w-full">
        <AccordionItem value={collection.id} className="border-0">
          <div className="px-4 md:px-6 py-3 md:py-4 flex justify-between w-full border-b relative min-h-[80px]">
            <div className="flex items-start gap-4">
              {/* Collection Info */}
              <div className="flex flex-col items-start text-left gap-2 sm:gap-1.5">
                <div className="flex flex-wrap items-center gap-2">
                  <Link
                    href={`/dashboard/platform/collections/${collection.id}`}
                    className="font-medium text-base hover:text-blue-600 dark:hover:text-blue-400"
                  >
                    {collection.title}
                  </Link>
                  <span>‧</span>

                  <span className="text-sm font-medium">
                    <span className="text-muted-foreground/75">
                      {new Date(collection.createdAt).toLocaleDateString("en-US", {
                        year: "numeric",
                        month: "long",
                        day: "numeric",
                      })}
                    </span>
                  </span>
                </div>

                {collection.handle && (
                  <span className="text-muted-foreground text-xs">
                    {collection.handle}
                  </span>
                )}
                <div className="flex flex-wrap items-center gap-1.5 text-xs text-muted-foreground">
                  <div className="flex items-center gap-1.5">
                    <Package className="size-3" />
                    <span className="font-medium">{productsCount}</span>
                    <span>product{productsCount !== 1 ? "s" : ""}</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex flex-col justify-between h-full">
              <div className="flex items-center gap-2">
                <Badge
                  variant="secondary"
                  className="text-[.6rem] sm:text-[.7rem] py-0 px-2 sm:px-3 tracking-wide font-medium rounded-md border h-6"
                >
                  COLLECTION
                </Badge>
                {/* Single buttons container */}
                <div className="absolute bottom-3 right-5 sm:static flex items-center gap-2">
                  <Button
                    variant="secondary"
                    size="icon"
                    className="border [&_svg]:size-3 h-6 w-6"
                    onClick={() => setIsEditDrawerOpen(true)}
                  >
                    <MoreVertical className="stroke-muted-foreground" />
                  </Button>
                  
                  {/* Collection Details Popover */}
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="secondary"
                        size="icon"
                        className="border [&_svg]:size-3 h-6 w-6"
                      >
                        <Package className="stroke-muted-foreground" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-80" align="end">
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <h4 className="font-medium text-sm">Collection Details</h4>
                          <div className="grid grid-cols-2 gap-3 text-sm">
                            <div>
                              <span className="text-muted-foreground">Handle:</span>
                              <div className="font-medium">
                                {collection.handle || "Not set"}
                              </div>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Products:</span>
                              <div className="font-medium">
                                {productsCount}
                              </div>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Created:</span>
                              <div className="font-medium">
                                {format(new Date(collection.createdAt), 'PP')}
                              </div>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Updated:</span>
                              <div className="font-medium">
                                {format(new Date(collection.updatedAt), 'PP')}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </PopoverContent>
                  </Popover>

                  <AccordionTrigger
                    className="border-0 p-0 h-6 w-6 flex items-center justify-center hover:no-underline [&[data-state=open]>svg]:rotate-180"
                    asChild
                  >
                    <Button
                      variant="secondary"
                      size="icon"
                      className="border [&_svg]:size-3 h-6 w-6 transition-transform"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="stroke-muted-foreground transition-transform duration-200"
                      >
                        <path d="m6 9 6 6 6-6" />
                      </svg>
                    </Button>
                  </AccordionTrigger>
                </div>
              </div>
            </div>
          </div>

          <AccordionContent className="px-4 md:px-6 pb-4">
            {products.length > 0 && (
              <div className="pt-4">
                <h4 className="text-sm font-medium mb-3">Products in this collection</h4>
                <div className="space-y-2">
                  {products.map((product) => (
                    <div
                      key={product.id}
                      className="flex items-center gap-3 p-3 rounded-md border border-border bg-background/50"
                    >
                      {product.thumbnail && (
                        <div className="h-10 w-10 rounded-md overflow-hidden bg-muted flex items-center justify-center">
                          <img 
                            src={product.thumbnail} 
                            alt={product.title} 
                            className="h-full w-full object-cover"
                          />
                        </div>
                      )}
                      {!product.thumbnail && (
                        <div className="h-10 w-10 rounded-md bg-muted flex items-center justify-center">
                          <Package className="h-5 w-5 text-muted-foreground" />
                        </div>
                      )}
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">{product.title}</p>
                        <p className="text-xs text-muted-foreground truncate">{product.handle}</p>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {product.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      <EditItemDrawer
        open={isEditDrawerOpen}
        onClose={() => setIsEditDrawerOpen(false)}
        listKey="product-collections"
        itemId={collection.id}
      />
    </>
  );
}