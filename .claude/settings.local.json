{"permissions": {"allow": ["Bash(rg:*)", "Bash(/Users/<USER>/.nvm/versions/node/v20.9.0/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -o \"from [''\"\"]([^''\"\"]+)[''\"\"]|require\\([''\"\"]([^''\"\"]+)[''\"\"]\\)\" -r '$1$2' -g '*.ts' -g '*.tsx' -g '*.js' -g '*.jsx')", "Bash(find:*)", "Bash(grep:*)", "Bash(npm install:*)", "Bash(npm run generate-views-index:*)", "Bash(ls:*)", "Bash(node:*)", "Bash(rm:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(cp:*)", "<PERSON><PERSON>(mv:*)", "Bash(npm run build:*)", "mcp__ide__getDiagnostics", "mcp__mcp-midscene__midscene_screenshot", "mcp__mcp-midscene__midscene_aiTap", "mcp__mcp-midscene__midscene_aiAssert", "<PERSON><PERSON>(node test-mutations.js)", "mcp__mcp-midscene__midscene_aiWaitFor", "Bash(npm install:*)", "<PERSON><PERSON>(sed:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "WebFetch(domain:localhost)", "WebFetch(domain:help.shopify.com)", "Bash(npx tsc:*)", "mcp__playwright__browser_navigate", "mcp__playwright__browser_click", "mcp__mcp-midscene__midscene_navigate", "mcp__playwright__browser_snapshot", "<PERSON><PERSON>(touch:*)"], "deny": []}}