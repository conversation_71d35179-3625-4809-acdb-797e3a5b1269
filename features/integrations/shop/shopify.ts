import { GraphQLClient, gql } from "graphql-request";

interface ShopifyPlatform {
  domain: string;
  accessToken: string;
}

interface SearchProductsArgs {
  searchEntry: string;
  after?: string;
}

interface GetProductArgs {
  productId: string;
  variantId?: string;
}

interface SearchOrdersArgs {
  searchEntry: string;
  after?: string;
}

interface UpdateProductArgs {
  productId: string;
  variantId: string;
  inventory?: number;
  price?: string;
}

interface CreateWebhookArgs {
  endpoint: string;
  events: string[];
}

interface DeleteWebhookArgs {
  webhookId: string;
}

interface OAuthArgs {
  callbackUrl: string;
}

interface OAuthCallbackArgs {
  code: string;
  shop: string;
  state: string;
}

interface WebhookEventArgs {
  event: any;
  headers: Record<string, string>;
}

// Function to search products
export async function searchProductsFunction({ 
  platform, 
  searchEntry, 
  after 
}: { 
  platform: ShopifyPlatform; 
  searchEntry: string; 
  after?: string; 
}) {
  const shopifyClient = new GraphQLClient(
    `https://${platform.domain}/admin/api/graphql.json`,
    {
      headers: {
        "X-Shopify-Access-Token": platform.accessToken,
      },
    }
  );

  const gqlQuery = gql`
    query SearchProducts($query: String, $after: String) {
      productVariants(first: 15, query: $query, after: $after) {
        edges {
          node {
            id
            availableForSale
            image {
              originalSrc
            }
            price
            title
            product {
              id
              handle
              title
              images(first: 1) {
                edges {
                  node {
                    originalSrc
                  }
                }
              }
            }
            inventoryQuantity
            inventoryPolicy
          }
          cursor
        }
        pageInfo {
          hasNextPage
          endCursor
        }
      }
    }
  `;

  const { productVariants } = await shopifyClient.request(gqlQuery, {
    query: searchEntry,
    after,
  });

  if (productVariants.edges.length < 1) {
    throw new Error("No products found from Shopify");
  }

  const products = productVariants.edges.map(({ node, cursor }) => ({
    image:
      node.image?.originalSrc || node.product.images.edges[0]?.node.originalSrc,
    title: `${node.product.title} - ${node.title}`,
    productId: node.product.id.split("/").pop(),
    variantId: node.id.split("/").pop(),
    price: node.price,
    availableForSale: node.availableForSale,
    inventory: node.inventoryQuantity,
    inventoryTracked: node.inventoryPolicy !== "deny",
    productLink: `https://${platform.domain}/products/${node.product.handle}`,
    cursor,
  }));

  return { 
    products, 
    pageInfo: productVariants.pageInfo 
  };
}

// Function to get a specific product by variantId and productId
export async function getProductFunction({
  platform,
  productId,
  variantId,
}: {
  platform: ShopifyPlatform;
  productId: string;
  variantId?: string;
}) {
  const shopifyClient = new GraphQLClient(
    `https://${platform.domain}/admin/api/graphql.json`,
    {
      headers: {
        "X-Shopify-Access-Token": platform.accessToken,
      },
    }
  );

  const gqlQuery = gql`
    query GetProduct($variantId: ID!) {
      productVariant(id: $variantId) {
        id
        availableForSale
        image {
          originalSrc
        }
        price
        title
        product {
          id
          handle
          title
          images(first: 1) {
            edges {
              node {
                originalSrc
              }
            }
          }
        }
        inventoryQuantity
        inventoryPolicy
      }
    }
  `;

  const { productVariant } = await shopifyClient.request(gqlQuery, {
    variantId: `gid://shopify/ProductVariant/${variantId || productId}`,
  });

  if (!productVariant) {
    throw new Error("Product not found from Shopify");
  }

  const product = {
    image:
      productVariant.image?.originalSrc ||
      productVariant.product.images.edges[0]?.node.originalSrc,
    title: `${productVariant.product.title} - ${productVariant.title}`,
    productId: productVariant.product.id.split("/").pop(),
    variantId: productVariant.id.split("/").pop(),
    price: productVariant.price,
    availableForSale: productVariant.availableForSale,
    inventory: productVariant.inventoryQuantity,
    inventoryTracked: productVariant.inventoryPolicy !== "deny",
    productLink: `https://${platform.domain}/admin/products/${productVariant.product.id
      .split("/")
      .pop()}/variants/${productVariant.id.split("/").pop()}`,
  };

  return { product };
}

export async function searchOrdersFunction({
  platform,
  searchEntry,
  after,
}: {
  platform: ShopifyPlatform;
  searchEntry: string;
  after?: string;
}) {
  const shopifyClient = new GraphQLClient(
    `https://${platform.domain}/admin/api/graphql.json`,
    {
      headers: {
        "X-Shopify-Access-Token": platform.accessToken,
      },
    }
  );

  const gqlQuery = gql`
    query SearchOrders($query: String, $after: String) {
      orders(first: 15, query: $query, after: $after) {
        edges {
          node {
            id
            name
            email
            createdAt
            updatedAt
            displayFulfillmentStatus
            displayFinancialStatus
            totalPriceSet {
              presentmentMoney {
                amount
                currencyCode
              }
            }
            shippingAddress {
              firstName
              lastName
              address1
              address2
              city
              province
              zip
              country
            }
            lineItems(first: 10) {
              edges {
                node {
                  id
                  title
                  quantity
                  image {
                    originalSrc
                  }
                  variant {
                    id
                    title
                    price
                    product {
                      id
                      title
                      handle
                    }
                  }
                }
              }
            }
          }
          cursor
        }
        pageInfo {
          hasNextPage
          endCursor
        }
      }
    }
  `;

  const { orders } = await shopifyClient.request(gqlQuery, {
    query: searchEntry,
    after,
  });

  const formattedOrders = orders.edges.map(({ node, cursor }) => ({
    orderId: node.id.split("/").pop(),
    orderName: node.name,
    link: `https://${platform.domain}/admin/orders/${node.id.split("/").pop()}`,
    date: new Date(node.createdAt).toLocaleDateString(),
    firstName: node.shippingAddress?.firstName || "",
    lastName: node.shippingAddress?.lastName || "",
    streetAddress1: node.shippingAddress?.address1 || "",
    streetAddress2: node.shippingAddress?.address2 || "",
    city: node.shippingAddress?.city || "",
    state: node.shippingAddress?.province || "",
    zip: node.shippingAddress?.zip || "",
    country: node.shippingAddress?.country || "",
    email: node.email || "",
    fulfillmentStatus: node.displayFulfillmentStatus,
    financialStatus: node.displayFinancialStatus,
    totalPrice: node.totalPriceSet.presentmentMoney.amount,
    currency: node.totalPriceSet.presentmentMoney.currencyCode,
    lineItems: node.lineItems.edges.map(({ node: lineItem }) => ({
      lineItemId: lineItem.id.split("/").pop(),
      name: lineItem.title,
      quantity: lineItem.quantity,
      image: lineItem.image?.originalSrc || "",
      price: lineItem.variant?.price || "0",
      variantId: lineItem.variant?.id.split("/").pop(),
      productId: lineItem.variant?.product.id.split("/").pop(),
    })),
    cartItems: [], // Would be populated if this order has cart items
    fulfillments: [], // Would need to be fetched if needed
    note: "",
    cursor,
  }));

  return { 
    orders: formattedOrders, 
    pageInfo: orders.pageInfo 
  };
}

export async function updateProductFunction({
  platform,
  productId,
  variantId,
  inventory,
  price,
}: {
  platform: ShopifyPlatform;
  productId: string;
  variantId: string;
  inventory?: number;
  price?: string;
}) {
  const shopifyClient = new GraphQLClient(
    `https://${platform.domain}/admin/api/graphql.json`,
    {
      headers: {
        "X-Shopify-Access-Token": platform.accessToken,
      },
    }
  );

  const mutations = [];

  if (price !== undefined) {
    const updatePriceMutation = gql`
      mutation UpdateProductVariantPrice($input: ProductVariantInput!) {
        productVariantUpdate(input: $input) {
          productVariant {
            id
            price
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

    mutations.push(
      shopifyClient.request(updatePriceMutation, {
        input: {
          id: `gid://shopify/ProductVariant/${variantId}`,
          price: price,
        },
      })
    );
  }

  if (inventory !== undefined) {
    const updateInventoryMutation = gql`
      mutation UpdateInventoryLevel($input: InventoryLevelInput!) {
        inventoryLevelUpdate(input: $input) {
          inventoryLevel {
            id
            available
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

    mutations.push(
      shopifyClient.request(updateInventoryMutation, {
        input: {
          inventoryItemId: `gid://shopify/InventoryItem/${variantId}`,
          locationId: platform.locationId, // This would need to be stored in the platform config
          available: inventory,
        },
      })
    );
  }

  const results = await Promise.all(mutations);
  return { success: true, results };
}

export async function createWebhookFunction({
  platform,
  endpoint,
  events,
}: {
  platform: ShopifyPlatform;
  endpoint: string;
  events: string[];
}) {
  const mapTopic = {
    ORDER_CREATED: "ORDERS_CREATE",
    ORDER_CANCELLED: "ORDERS_CANCELLED", 
    ORDER_CHARGEBACKED: "DISPUTES_CREATE",
    TRACKING_CREATED: "FULFILLMENTS_CREATE",
  };

  const shopifyClient = new GraphQLClient(
    `https://${platform.domain}/admin/api/graphql.json`,
    {
      headers: {
        "X-Shopify-Access-Token": platform.accessToken,
      },
    }
  );

  const webhooks = [];

  for (const event of events) {
    const shopifyTopic = mapTopic[event] || event;
    const mutation = gql`
      mutation CreateWebhook($input: WebhookSubscriptionInput!) {
        webhookSubscriptionCreate(input: $input) {
          webhookSubscription {
            id
            callbackUrl
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

    const result = await shopifyClient.request(mutation, {
      input: {
        callbackUrl: endpoint,
        topic: shopifyTopic,
        format: "JSON",
      },
    });

    if (result.webhookSubscriptionCreate.userErrors.length > 0) {
      throw new Error(
        `Error creating webhook: ${result.webhookSubscriptionCreate.userErrors[0].message}`
      );
    }

    webhooks.push(result.webhookSubscriptionCreate.webhookSubscription);
  }

  // Return the first webhook's ID for compatibility with existing code
  const webhookId = webhooks[0]?.id?.split("/").pop();
  return { webhooks, webhookId };
}

export async function deleteWebhookFunction({
  platform,
  webhookId,
}: {
  platform: ShopifyPlatform;
  webhookId: string;
}) {
  const shopifyClient = new GraphQLClient(
    `https://${platform.domain}/admin/api/graphql.json`,
    {
      headers: {
        "X-Shopify-Access-Token": platform.accessToken,
      },
    }
  );

  const mutation = gql`
    mutation DeleteWebhook($id: ID!) {
      webhookSubscriptionDelete(id: $id) {
        deletedWebhookSubscriptionId
        userErrors {
          field
          message
        }
      }
    }
  `;

  const result = await shopifyClient.request(mutation, {
    id: `gid://shopify/WebhookSubscription/${webhookId}`,
  });

  return result.webhookSubscriptionDelete;
}

export async function getWebhooksFunction({
  platform,
}: {
  platform: ShopifyPlatform;
}) {
  const mapTopic = {
    ORDERS_CREATE: "ORDER_CREATED",
    ORDERS_CANCELLED: "ORDER_CANCELLED",
    DISPUTES_CREATE: "ORDER_CHARGEBACKED",
    FULFILLMENTS_CREATE: "TRACKING_CREATED",
  };

  const shopifyClient = new GraphQLClient(
    `https://${platform.domain}/admin/api/graphql.json`,
    {
      headers: {
        "X-Shopify-Access-Token": platform.accessToken,
      },
    }
  );

  const query = gql`
    query GetWebhooks {
      webhookSubscriptions(first: 50) {
        edges {
          node {
            id
            callbackUrl
            topic
            format
            createdAt
            includeFields
          }
        }
      }
    }
  `;

  const { webhookSubscriptions } = await shopifyClient.request(query);

  const webhooks = webhookSubscriptions.edges.map(({ node }) => ({
    id: node.id.split("/").pop(),
    callbackUrl: node.callbackUrl.replace(process.env.FRONTEND_URL || "", ""),
    topic: mapTopic[node.topic] || node.topic,
    format: node.format,
    createdAt: node.createdAt,
    includeFields: node.includeFields || [],
  }));

  return { webhooks };
}

export async function oAuthFunction({
  platform,
  callbackUrl,
}: {
  platform: ShopifyPlatform;
  callbackUrl: string;
}) {
  // This would typically redirect to Shopify's OAuth URL
  const scopes = "read_products,write_products,read_orders,write_orders,read_inventory,write_inventory";
  const shopifyAuthUrl = `https://${platform.domain}/admin/oauth/authorize?client_id=${process.env.SHOPIFY_APP_KEY}&scope=${scopes}&redirect_uri=${callbackUrl}&state=${Math.random().toString(36).substring(7)}`;
  
  return { authUrl: shopifyAuthUrl };
}

export async function oAuthCallbackFunction({
  platform,
  code,
  shop,
  state,
}: {
  platform: ShopifyPlatform;
  code: string;
  shop: string;
  state: string;
}) {
  const tokenUrl = `https://${shop}/admin/oauth/access_token`;
  
  const response = await fetch(tokenUrl, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      client_id: process.env.SHOPIFY_APP_KEY,
      client_secret: process.env.SHOPIFY_APP_SECRET,
      code,
    }),
  });

  if (!response.ok) {
    throw new Error("Failed to exchange OAuth code for access token");
  }

  const { access_token } = await response.json();
  
  return { 
    accessToken: access_token,
    domain: shop,
  };
}

export async function createOrderWebhookHandler({
  platform,
  event,
  headers,
}: {
  platform: ShopifyPlatform;
  event: any;
  headers: Record<string, string>;
}) {
  // Verify webhook authenticity
  const hmac = headers["x-shopify-hmac-sha256"];
  if (!hmac) {
    throw new Error("Missing webhook HMAC");
  }

  // Process the order data
  const order = {
    id: event.id,
    name: event.name,
    email: event.email,
    financialStatus: event.financial_status,
    fulfillmentStatus: event.fulfillment_status,
    lineItems: event.line_items.map((item) => ({
      id: item.id,
      title: item.title,
      quantity: item.quantity,
      variantId: item.variant_id,
      productId: item.product_id,
      price: item.price,
    })),
    shippingAddress: event.shipping_address,
    totalPrice: event.total_price,
    currency: event.currency,
  };

  return { order, type: "order_created" };
}

export async function cancelOrderWebhookHandler({
  platform,
  event,
  headers,
}: {
  platform: ShopifyPlatform;
  event: any;
  headers: Record<string, string>;
}) {
  // Verify webhook authenticity
  const hmac = headers["x-shopify-hmac-sha256"];
  if (!hmac) {
    throw new Error("Missing webhook HMAC");
  }

  const order = {
    id: event.id,
    name: event.name,
    cancelReason: event.cancel_reason,
    cancelledAt: event.cancelled_at,
  };

  return { order, type: "order_cancelled" };
}