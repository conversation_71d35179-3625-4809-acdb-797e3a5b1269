import { list } from "@keystone-6/core";
import { allOperations } from "@keystone-6/core/access";
import { json, relationship, text, timestamp } from "@keystone-6/core/fields";

import { isSignedIn, permissions, rules } from "../access";
import { trackingFields } from "./trackingFields";

export const Channel = list({
  access: {
    operation: {
      query: isSignedIn,
      create: permissions.canCreateChannels,
      update: isSignedIn,
      delete: permissions.canManageChannels,
    },
    filter: {
      query: rules.canReadChannels,
      update: rules.canManageChannels,
      delete: rules.canManageChannels,
    },
  },
  ui: {
    listView: {
      initialColumns: ["name", "domain", "platform"],
    },
  },
  fields: {
    name: text({
      validation: { isRequired: true },
    }),
    domain: text(),
    accessToken: text({
      ui: {
        displayMode: "textarea",
      },
    }),
    metadata: json({
      defaultValue: {},
    }),

    // Relationships
    platform: relationship({
      ref: "ChannelPlatform.channels",
    }),
    user: relationship({
      ref: "User.channels",
      hooks: {
        resolveInput: ({ operation, resolvedData, context }) => {
          if (
            operation === "create" &&
            !resolvedData.user &&
            context.session?.itemId
          ) {
            return { connect: { id: context.session.itemId } };
          }
          return resolvedData.user;
        },
      },
    }),
    links: relationship({
      ref: "Link.channel",
      many: true,
    }),
    channelItems: relationship({
      ref: "ChannelItem.channel",
      many: true,
    }),
    cartItems: relationship({
      ref: "CartItem.channel",
      many: true,
    }),

    ...trackingFields,
  },
});
