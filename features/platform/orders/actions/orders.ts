'use server';

import { keystoneClient } from "@/features/dashboard/lib/keystoneClient";

/**
 * Search for products in a channel
 */
export async function searchChannelProducts(channelId: string, searchEntry: string) {
  const query = `
    query SearchChannelProducts($channelId: ID!, $searchEntry: String!) {
      searchChannelProducts(channelId: $channelId, searchEntry: $searchEntry) {
        image
        title
        productId
        variantId
        price
      }
    }
  `;

  const response = await keystoneClient(query, { channelId, searchEntry });

  if (response.error) {
    throw new Error(response.error);
  }

  return response.data?.searchChannelProducts;
}
