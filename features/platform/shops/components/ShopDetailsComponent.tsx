"use client";

import React, { useState } from "react";
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ChevronDown, MoreVertical, PenSquare, ExternalLink, Store, Ticket, SquareStack, LinkIcon, Webhook } from "lucide-react";
import Link from "next/link";
import { ArrowRight } from "lucide-react";
import { EditItemDrawer } from "@/features/platform/components/EditItemDrawer";
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import { SearchOrders } from "./SearchOrders";
import { Links } from "./Links";
import { Webhooks } from "./Webhooks";
import MatchList from "./MatchList";
import type { Shop } from "../lib/types";

interface ShopDetailsComponentProps {
  shop: Shop;
  loadingActions?: Record<string, Record<string, boolean>>;
  removeEditItemButton?: boolean;
  renderButtons?: () => React.ReactNode;
}

export const ShopDetailsComponent = ({
  shop,
  loadingActions = {},
  removeEditItemButton,
  renderButtons,
}: ShopDetailsComponentProps) => {
  const [isEditDrawerOpen, setIsEditDrawerOpen] = useState(false);
  
  const currentAction = Object.entries(loadingActions).find(
    ([_, value]) => value[shop.id]
  )?.[0];

  const getLoadingText = (action: string) => {
    switch (action) {
      case "deleteShop":
        return "Deleting Shop";
      default:
        return "Loading";
    }
  };

  const ordersCount = shop.orders?.length || 0;
  const itemsCount = shop.shopItems?.length || 0;
  const linksCount = shop.links?.length || 0;

  const tabsData = [
    { value: "orders", icon: Ticket, label: "Orders", count: ordersCount },
    { value: "matches", icon: SquareStack, label: "Matches", count: itemsCount },
    { value: "links", icon: LinkIcon, label: "Links", count: linksCount },
    { value: "webhooks", icon: Webhook, label: "Webhooks" },
  ];

  return (
    <>
      <div className="flex flex-col border-b">
        <div className="px-4 md:px-6 py-3 md:py-4 flex justify-between w-full relative">
            <div className="flex flex-col items-start text-left gap-2 sm:gap-1.5">
              <div className="flex flex-wrap items-center gap-2 sm:gap-4">
                <div className="flex items-center gap-2">
                  <Store className="h-4 w-4 text-muted-foreground" />
                  <Link
                    href={`/dashboard/platform/shops/${shop.id}`}
                    className="font-medium text-sm hover:text-blue-600 dark:hover:text-blue-400"
                  >
                    {shop.name}
                  </Link>
                </div>
                
                {shop.domain && (
                  <a
                    href={`https://${shop.domain}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-xs text-muted-foreground hover:text-blue-600 dark:hover:text-blue-400 flex items-center gap-1"
                  >
                    {shop.domain}
                    <ExternalLink className="h-3 w-3" />
                  </a>
                )}

                <span className="text-xs font-medium">
                  <span className="text-muted-foreground/75">
                    {new Date(shop.createdAt).toLocaleDateString("en-US", {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                      hour: "numeric",
                      minute: "2-digit",
                    })}
                  </span>
                  {shop.user && (
                    <>
                      <span className="mx-1.5">‧</span>
                      <Link
                        href={`/dashboard/users/${shop.user.id}`}
                        className="text-muted-foreground hover:text-blue-600 dark:hover:text-blue-400 group inline-flex items-center gap-1"
                      >
                        {shop.user.name || shop.user.email}
                        <ArrowRight className="h-3 w-3 opacity-0 -translate-x-2 transition-all group-hover:opacity-100 group-hover:translate-x-0" />
                      </Link>
                    </>
                  )}
                </span>
              </div>

              <div className="flex flex-wrap gap-4 text-xs sm:text-sm text-muted-foreground mt-1">
                {shop.platform && (
                  <div className="flex items-center gap-1">
                    <span>Platform:</span>
                    <Badge variant="outline" className="text-xs">
                      {shop.platform.name}
                    </Badge>
                  </div>
                )}
                
                <div className="flex items-center gap-4">
                  <span>{ordersCount} orders</span>
                  <span>{itemsCount} items</span>
                  <span>{linksCount} links</span>
                </div>
              </div>
            </div>

            <div className="flex flex-col justify-between h-full">
              <div className="flex items-center gap-2">
                {currentAction && (
                  <Badge
                    color="zinc"
                    className="uppercase tracking-wide font-medium text-xs flex items-center gap-1.5 border py-0.5"
                  >
                    <div className="size-3.5 shrink-0 animate-spin" />
                    {getLoadingText(currentAction)}
                  </Badge>
                )}
                
                {/* Single buttons container */}
                <div className="absolute bottom-3 right-5 sm:static flex items-center gap-2">
                  {!removeEditItemButton && (
                    <Button
                      variant="secondary"
                      size="icon"
                      className="border [&_svg]:size-3 h-6 w-6"
                      onClick={() => setIsEditDrawerOpen(true)}
                    >
                      <MoreVertical className="stroke-muted-foreground" />
                    </Button>
                  )}
                  {renderButtons && renderButtons()}
                </div>
              </div>
            </div>
          </div>
          
          <Tabs defaultValue="orders" className="w-full">
            <ScrollArea>
              <TabsList className="justify-start w-full h-auto gap-2 rounded-none bg-transparent px-4 md:px-6 py-0 border-b-0">
                {tabsData.map((tab) => (
                  <TabsTrigger
                    key={tab.value}
                    value={tab.value}
                    className="text-muted-foreground relative pb-3 pt-2 px-3 data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:text-foreground hover:text-foreground transition-colors after:absolute after:bottom-0 after:left-0 after:right-0 after:h-[2px] data-[state=active]:after:bg-blue-600 rounded-none"
                  >
                    <tab.icon
                      className="-ms-0.5 me-1.5 opacity-60"
                      size={16}
                      strokeWidth={2}
                      aria-hidden="true"
                    />
                    <span className="hidden sm:inline">{tab.label}</span>
                    {tab.count !== undefined && tab.count > 0 && (
                      <span className="ml-1.5 text-xs text-muted-foreground">
                        {tab.count}
                      </span>
                    )}
                  </TabsTrigger>
                ))}
              </TabsList>
              <ScrollBar orientation="horizontal" />
            </ScrollArea>

            <TabsContent value="orders" className="bg-background p-4 md:p-6 border-t mt-0">
              <SearchOrders
                shopId={shop.id}
                searchEntry=""
                pageSize={10}
              />
            </TabsContent>

            <TabsContent value="matches" className="bg-background p-4 md:p-6 border-t mt-0">
              <MatchList
                shopId={shop.id}
                onMatchAction={() => {}}
                showCreate={false}
              />
            </TabsContent>

            <TabsContent value="links" className="bg-background p-4 md:p-6 border-t mt-0">
              <Links
                shopId={shop.id}
                links={shop.links || []}
                refetch={() => {}}
                linkMode={shop.linkMode || "sequential"}
                editItem={(id, type) => {}}
              />
            </TabsContent>

            <TabsContent value="webhooks" className="bg-background p-4 md:p-6 border-t mt-0">
              <div className="space-y-4">
                <div className="text-sm text-muted-foreground mb-4">
                  Create platform webhooks to keep Openship in sync
                </div>
                <Webhooks shopId={shop.id} />
              </div>
            </TabsContent>
          </Tabs>
        </div>

      <EditItemDrawer
        listKey="Shop"
        itemId={shop.id}
        open={isEditDrawerOpen}
        onClose={() => setIsEditDrawerOpen(false)}
      />
    </>
  );
};