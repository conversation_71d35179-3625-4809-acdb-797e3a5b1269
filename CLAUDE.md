# OpenShip to OpenShip Dasher Migration Plan

## Overview
This document outlines the comprehensive migration plan for rewriting OpenShip into OpenShip Dasher, leveraging the modern Keystone 6+ architecture and feature-slicing patterns from Dasher 7.

## Project References
- **OpenShip Dasher** (Target): `/Users/<USER>/openship-dasher` - Modern Keystone 6+ with Next.js 14+ and feature slicing
- **OpenShip** (Source): `/Users/<USER>/openship` - Legacy application containing business logic and models
- **Dasher 7** (Pattern Reference): Reference for architecture patterns, feature slicing, and UI components

## Architecture Strategy

### Feature-Slicing Approach
Following Dasher 7's architecture, we'll organize code by features rather than technical layers:
```
features/
├── keystone/        # Backend models, access control, mutations
├── dashboard/       # Admin interface and management
├── platform/        # Platform integrations (Shopify, BigCommerce, etc.)
├── order/          # Order processing and management
├── inventory/      # Inventory synchronization
└── auth/           # Authentication and user management
```

## Major Objectives

### Phase 1: Keystone Foundation 🎯
**Goal**: Establish the core Keystone backend with all OpenShip models

#### 1.1 Model Migration Strategy
**Current Status**: Basic User, Role, Todo models exist
**Target**: Migrate all 12 core OpenShip models with modern patterns

**Models to Migrate**:
1. **User Management Models**:
   - ✅ `User` (exists, needs enhancement)
   - ✅ `Role` (exists, needs enhancement) 
   - 🔄 `ApiKey` (new - improve from OpenShip pattern using Dasher 7 auth)

2. **Platform Integration Models**:
   - 🆕 `ShopPlatform` - Shop platform definitions (Shopify, BigCommerce, WooCommerce)
   - 🆕 `ChannelPlatform` - Sales channel platform definitions
   - 🆕 `Shop` - Individual shop instances
   - 🆕 `Channel` - Individual sales channel instances

3. **Product & Inventory Models**:
   - 🆕 `ShopItem` - Products from connected shops
   - 🆕 `ChannelItem` - Products on sales channels  
   - 🆕 `Match` - Shop-to-channel item matching for inventory sync

4. **Order Management Models**:
   - 🆕 `Order` - Master order entity with processing logic
   - 🆕 `LineItem` - Original order items from shops
   - 🆕 `CartItem` - Items to be purchased from channels
   - 🆕 `TrackingDetail` - Shipping tracking information

5. **Linking System Models**:
   - 🆕 `Link` - Dynamic shop-to-channel linking with filters

#### 1.2 Access Control Enhancement
- **Implement role-based permissions** following Dasher 7 patterns
- **Add row-level security** for multi-tenant data isolation
- **Create granular field-level access** controls
- **Establish user-scoped data access** rules

#### 1.3 Authentication Modernization
- **Improve API key handling** following Dasher 7 auth patterns
- **Implement session management** with httpOnly cookies
- **Add OAuth integration** for platform connections
- **Create middleware authentication** for route protection

### Phase 2: Dashboard Integration 🖥️
**Goal**: Create modern admin interface following Dasher 7 UI patterns

#### 2.1 Dashboard Foundation
- **Adapt DashboardLayout** from Dasher 7 patterns
- **Implement AdminMetaProvider** for dynamic UI generation
- **Create responsive navigation** with sidebar and breadcrumbs
- **Add theme switching** and user profile management

#### 2.2 Model Management UI
- **Generate dynamic list pages** for all OpenShip models
- **Create item detail/edit views** with proper field rendering
- **Implement search and filtering** capabilities
- **Add pagination and sorting** functionality

#### 2.3 Field System Integration
- **Adapt field views** from Dasher 7 for OpenShip-specific needs
- **Create custom field types** for OpenShip data (platforms, API configs)
- **Implement relationship fields** for complex model connections
- **Add file upload support** for platform assets

### Phase 3: Platform Feature Implementation 🔌
**Goal**: Rebuild platform integration system with modern architecture

#### 3.1 Platform Architecture
- **Create platform adapter system** using feature-slicing patterns
- **Implement OAuth flows** for platform authentication
- **Add webhook handling** for real-time platform updates
- **Create platform configuration** UI components

#### 3.2 Platform-Specific Features
- **Shopify integration** with modern API patterns
- **BigCommerce integration** with improved error handling
- **WooCommerce integration** with enhanced security
- **Extensible platform registry** for adding new platforms

#### 3.3 Platform UI Components
- **Platform selection dialogs** following Dasher 7 UI patterns
- **OAuth connection flows** with proper error handling
- **Configuration forms** with dynamic field generation
- **Status monitoring dashboards** for platform health

### Phase 4: Order Processing System 📦
**Goal**: Implement sophisticated order management with modern patterns

#### 4.1 Order Workflow Engine
- **Rebuild order processing** logic with improved error handling
- **Implement dynamic linking** system with filter conditions
- **Add order hooks** for automated processing
- **Create order status tracking** with real-time updates

#### 4.2 Inventory Synchronization
- **Implement match-based sync** between shops and channels
- **Add real-time inventory** updates via webhooks
- **Create conflict resolution** for inventory discrepancies
- **Add bulk operations** for large inventory changes

#### 4.3 Order Management UI
- **Create order dashboard** with filtering and search
- **Implement order detail views** with tracking information
- **Add order processing controls** with status management
- **Create reporting interfaces** for order analytics

### Phase 5: Advanced Features & Polish ✨
**Goal**: Add sophisticated features and optimize user experience

#### 5.1 Advanced Functionality
- **Implement order automation** rules and triggers
- **Add advanced filtering** with dynamic where clauses
- **Create batch operations** for bulk data management
- **Add data export/import** capabilities

#### 5.2 Performance & Scalability
- **Optimize GraphQL queries** with proper field selection
- **Implement caching strategies** for frequently accessed data
- **Add database indexing** for improved query performance
- **Create API rate limiting** for external platform calls

#### 5.3 Developer Experience
- **Add comprehensive logging** for debugging platform issues
- **Create API documentation** for custom integrations
- **Implement testing framework** for order processing logic
- **Add development tools** for platform testing

## Technical Implementation Details

### Authentication Strategy
Following Dasher 7 patterns but enhanced for OpenShip's multi-platform needs:

```typescript
// Enhanced session data for OpenShip
sessionData: `
  id name email role {
    permissions
    canAccessPlatforms
    assignedShops
  }
  connectedPlatforms {
    id platform accessToken
  }
`
```

### API Key Management
Improved from OpenShip using Dasher 7 patterns:

```typescript
// Modern API key model
export const ApiKey = list({
  access: {
    operation: {
      create: isAdmin,
      query: isOwnerOrAdmin,
      update: isOwnerOrAdmin,
      delete: isOwnerOrAdmin
    }
  },
  fields: {
    name: text({ validation: { isRequired: true } }),
    key: text({ isIndexed: 'unique', access: { read: denyAll } }),
    permissions: json({ defaultValue: [] }),
    lastUsed: timestamp(),
    expiresAt: timestamp(),
    owner: relationship({ ref: 'User.apiKeys' })
  }
})
```

### Platform Integration Architecture
```typescript
// Platform adapter interface
interface PlatformAdapter {
  authenticate(credentials: PlatformCredentials): Promise<AuthResult>
  fetchOrders(config: FetchConfig): Promise<Order[]>
  createOrder(order: OrderInput): Promise<OrderResult>
  updateInventory(items: InventoryUpdate[]): Promise<UpdateResult>
}
```

### Feature Organization
```
features/
├── keystone/
│   ├── models/          # All data models
│   ├── access/          # Access control rules
│   ├── mutations/       # Custom GraphQL mutations
│   └── schema.ts        # Schema assembly
├── dashboard/
│   ├── components/      # Admin UI components
│   ├── screens/         # Page-level components
│   ├── actions/         # Server actions
│   └── lib/            # Dashboard utilities
├── platform/
│   ├── adapters/        # Platform-specific adapters
│   ├── components/      # Platform UI components
│   ├── hooks/          # Platform integration hooks
│   └── types/          # Platform type definitions
└── order/
    ├── processing/      # Order processing logic
    ├── components/      # Order UI components
    ├── hooks/          # Order management hooks
    └── lib/            # Order utilities
```

## Migration Steps & Timeline

### Immediate Next Steps (Phase 1A)
1. **Create comprehensive todo list** for Phase 1 model migration
2. **Start with User/Role enhancement** to match OpenShip capabilities
3. **Implement ApiKey model** with improved Dasher 7 patterns
4. **Set up basic access control** framework

### Short-term Goals (Phases 1B-1C)
1. **Migrate platform models** (ShopPlatform, ChannelPlatform, Shop, Channel)
2. **Add product models** (ShopItem, ChannelItem, Match)
3. **Implement order models** (Order, LineItem, CartItem, TrackingDetail)
4. **Create Link model** for dynamic connections

### Medium-term Goals (Phase 2)
1. **Adapt dashboard components** from Dasher 7
2. **Create model-specific UI** for OpenShip entities
3. **Implement search/filtering** for complex data
4. **Add relationship management** interfaces

### Long-term Goals (Phases 3-5)
1. **Rebuild platform integrations** with modern patterns
2. **Implement order processing** workflows
3. **Add advanced features** and optimizations
4. **Polish user experience** and performance

## Success Criteria
- ✅ All OpenShip models successfully migrated to Keystone 6+
- ✅ Dashboard provides full CRUD operations for all entities
- ✅ Platform integrations work with OAuth and webhooks
- ✅ Order processing maintains OpenShip's sophisticated logic
- ✅ Authentication and access control match OpenShip's security
- ✅ UI follows Dasher 7's modern patterns and responsiveness
- ✅ Performance equals or exceeds original OpenShip
- ✅ Codebase follows feature-slicing best practices

## Notes & Considerations
- **Data Migration**: Plan for migrating existing OpenShip data to new schema
- **API Compatibility**: Consider maintaining backward compatibility for existing integrations  
- **Testing Strategy**: Implement comprehensive testing for order processing logic
- **Deployment**: Plan staging environment for testing platform integrations
- **Documentation**: Create comprehensive guides for new architecture

---

*This document will be updated as we progress through the migration phases.*