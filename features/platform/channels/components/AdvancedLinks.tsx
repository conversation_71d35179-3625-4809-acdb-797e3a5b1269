"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON>R<PERSON>,
  Edit2,
  Plus,
  Trash2,
  CircleAlert,
  ChevronDown,
  GripVertical,
  X,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { ReactSortable } from "react-sortablejs";
import { 
  getShops, 
  getChannelLinks, 
  createChannelLink, 
  deleteChannelLink,
  updateChannelLink 
} from "../actions/links";

interface LinkFilter {
  field: string;
  type: string;
  value: string;
}

interface Link {
  id: string;
  shop: {
    id: string;
    name: string;
  };
  filters: LinkFilter[];
  createdAt: string;
  rank?: number;
}

interface Shop {
  id: string;
  name: string;
}

interface SortableLink extends Link {
  chosen?: boolean;
  selected?: boolean;
}

export const CreateLinkButton = ({ channelId, refetch }: {
  channelId: string;
  refetch: () => void;
}) => {
  const [shops, setShops] = useState<Shop[]>([]);
  const [selectedShopId, setSelectedShopId] = useState<string>("");
  const [isCreating, setIsCreating] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    const loadShops = async () => {
      const response = await getShops();
      if (response.success && response.data) {
        setShops(response.data.shops);
      }
    };
    loadShops();
  }, []);

  const handleCreate = async () => {
    if (!selectedShopId) return;

    setIsCreating(true);
    try {
      const response = await createChannelLink(channelId, selectedShopId);
      if (response.success) {
        toast({
          title: "Link Created",
          description: "Successfully created shop link",
        });
        setIsOpen(false);
        setSelectedShopId("");
        refetch();
      } else {
        toast({
          title: "Error",
          description: response.error || "Failed to create link",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create link",
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button size="sm" className="gap-1">
          <Plus className="h-3 w-3" />
          Add Link
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create New Link</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <label className="text-sm font-medium">Shop</label>
            <Select value={selectedShopId} onValueChange={setSelectedShopId}>
              <SelectTrigger>
                <SelectValue placeholder="Select a shop..." />
              </SelectTrigger>
              <SelectContent>
                {shops.map((shop) => (
                  <SelectItem key={shop.id} value={shop.id}>
                    {shop.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleCreate} 
              disabled={!selectedShopId || isCreating}
            >
              {isCreating ? "Creating..." : "Create Link"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

const FilterManager = ({ link, onUpdate }: { 
  link: Link; 
  onUpdate: (filters: LinkFilter[]) => void; 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [newFilter, setNewFilter] = useState({
    field: "",
    type: "contains",
    value: ""
  });
  const { toast } = useToast();

  const filterTypes = [
    { value: "contains", label: "Contains" },
    { value: "equals", label: "Equals" },
    { value: "startsWith", label: "Starts With" },
    { value: "endsWith", label: "Ends With" },
  ];

  const orderFields = [
    { value: "orderName", label: "Order Name" },
    { value: "firstName", label: "First Name" },
    { value: "lastName", label: "Last Name" },
    { value: "email", label: "Email" },
    { value: "city", label: "City" },
    { value: "state", label: "State" },
    { value: "zip", label: "ZIP Code" },
  ];

  const handleAddFilter = () => {
    if (!newFilter.field || !newFilter.value) {
      toast({
        title: "Error",
        description: "Please fill in all filter fields",
        variant: "destructive",
      });
      return;
    }

    const updatedFilters = [...(link.filters || []), newFilter];
    onUpdate(updatedFilters);
    setNewFilter({ field: "", type: "contains", value: "" });
    setIsOpen(false);
  };

  const handleRemoveFilter = (index: number) => {
    const updatedFilters = (link.filters || []).filter((_, i) => i !== index);
    onUpdate(updatedFilters);
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium">Filters</h4>
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button size="sm" variant="outline" className="h-6 w-6 p-0">
              <Plus className="h-3 w-3" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80">
            <div className="space-y-3">
              <div>
                <Label htmlFor="field">Field</Label>
                <Select
                  value={newFilter.field}
                  onValueChange={(value) => setNewFilter(prev => ({ ...prev, field: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select field..." />
                  </SelectTrigger>
                  <SelectContent>
                    {orderFields.map((field) => (
                      <SelectItem key={field.value} value={field.value}>
                        {field.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="type">Type</Label>
                <Select
                  value={newFilter.type}
                  onValueChange={(value) => setNewFilter(prev => ({ ...prev, type: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {filterTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="value">Value</Label>
                <Input
                  id="value"
                  value={newFilter.value}
                  onChange={(e) => setNewFilter(prev => ({ ...prev, value: e.target.value }))}
                  placeholder="Enter filter value..."
                />
              </div>
              <div className="flex justify-end gap-2">
                <Button size="sm" variant="outline" onClick={() => setIsOpen(false)}>
                  Cancel
                </Button>
                <Button size="sm" onClick={handleAddFilter}>
                  Add Filter
                </Button>
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>
      
      {link.filters && link.filters.length > 0 && (
        <div className="space-y-1">
          {link.filters.map((filter, index) => (
            <div key={index} className="flex items-center justify-between bg-muted p-2 rounded text-xs">
              <span>
                <span className="font-medium">{filter.field}</span>{" "}
                <span className="text-muted-foreground">{filter.type}</span>{" "}
                <span className="font-medium">"{filter.value}"</span>
              </span>
              <Button
                size="sm"
                variant="ghost"
                className="h-4 w-4 p-0"
                onClick={() => handleRemoveFilter(index)}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          ))}
        </div>
      )}
      
      {(!link.filters || link.filters.length === 0) && (
        <p className="text-xs text-muted-foreground">No filters configured</p>
      )}
    </div>
  );
};

export function AdvancedLinks({ channelId }: { channelId: string }) {
  const [links, setLinks] = useState<SortableLink[]>([]);
  const [selectedLinkId, setSelectedLinkId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasOrderChanged, setHasOrderChanged] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const { toast } = useToast();

  const loadLinks = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await getChannelLinks(channelId);
      if (response.success && response.data) {
        const sortedLinks = response.data.links.sort((a: Link, b: Link) => (a.rank || 0) - (b.rank || 0));
        setLinks(sortedLinks);
      } else {
        setError(response.error || "Failed to load links");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load links");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadLinks();
  }, [channelId]);

  const handleDelete = async (linkId: string) => {
    try {
      const response = await deleteChannelLink(linkId);
      if (response.success) {
        toast({
          title: "Link Deleted",
          description: "Successfully deleted link",
        });
        loadLinks();
        if (selectedLinkId === linkId) {
          setSelectedLinkId(null);
        }
      } else {
        toast({
          title: "Error",
          description: response.error || "Failed to delete link",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete link",
        variant: "destructive",
      });
    }
  };

  const handleFilterUpdate = async (linkId: string, filters: LinkFilter[]) => {
    try {
      const response = await updateChannelLink(linkId, { filters });
      if (response.success) {
        toast({
          title: "Filters Updated",
          description: "Successfully updated link filters",
        });
        loadLinks();
      } else {
        toast({
          title: "Error",
          description: response.error || "Failed to update filters",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update filters",
        variant: "destructive",
      });
    }
  };

  const handleSaveOrder = async () => {
    setIsUpdating(true);
    try {
      // Update ranks based on current order
      const updatePromises = links.map((link, index) =>
        updateChannelLink(link.id, { rank: index + 1 })
      );
      
      await Promise.all(updatePromises);
      
      toast({
        title: "Order Updated",
        description: "Successfully updated link order",
      });
      
      setHasOrderChanged(false);
      loadLinks();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update link order",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleOrderChange = (newOrder: SortableLink[]) => {
    setLinks(newOrder);
    setHasOrderChanged(true);
  };

  if (error) {
    return (
      <div className="flex items-center gap-2 p-4 text-red-600">
        <CircleAlert className="h-4 w-4" />
        <span>{error}</span>
      </div>
    );
  }

  const selectedLink = selectedLinkId ? links.find(link => link.id === selectedLinkId) : null;

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
      {/* Links List */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-medium text-sm">Shop Links</h4>
            <p className="text-xs text-muted-foreground">
              Create links to shops with processing order
            </p>
          </div>
          <div className="flex gap-2">
            {hasOrderChanged && (
              <Button
                size="sm"
                onClick={handleSaveOrder}
                disabled={isUpdating}
                className="h-6 px-2"
              >
                {isUpdating ? "Saving..." : "Save Order"}
              </Button>
            )}
            <CreateLinkButton channelId={channelId} refetch={loadLinks} />
          </div>
        </div>

        {isLoading ? (
          <div className="space-y-2">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="h-16 bg-muted animate-pulse rounded" />
            ))}
          </div>
        ) : links.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <p>No shop links configured.</p>
            <p className="text-xs mt-1">
              Links connect shops to this channel for order processing.
            </p>
          </div>
        ) : (
          <ReactSortable
            list={links}
            setList={handleOrderChange}
            className="space-y-2"
            handle=".drag-handle"
          >
            {links.map((link, index) => (
              <div
                key={link.id}
                className={`flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 cursor-pointer transition-colors ${
                  selectedLinkId === link.id ? 'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950' : ''
                }`}
                onClick={() => setSelectedLinkId(link.id)}
              >
                <div className="flex items-center gap-3">
                  <div className="drag-handle cursor-move p-1">
                    <GripVertical className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <Badge variant="outline" className="text-xs">
                    #{index + 1}
                  </Badge>
                  <div>
                    <div className="font-medium text-sm">{link.shop.name}</div>
                    <div className="text-xs text-muted-foreground">
                      {(link.filters || []).length} filter{(link.filters || []).length !== 1 && "s"}
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDelete(link.id);
                    }}
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            ))}
          </ReactSortable>
        )}
      </div>

      {/* Filter Management */}
      {selectedLink && (
        <div className="space-y-4">
          <div>
            <h4 className="font-medium text-sm">Link Filters</h4>
            <p className="text-xs text-muted-foreground">
              Configure filters for {selectedLink.shop.name}
            </p>
          </div>
          <div className="border rounded-lg p-4">
            <FilterManager
              link={selectedLink}
              onUpdate={(filters) => handleFilterUpdate(selectedLink.id, filters)}
            />
          </div>
        </div>
      )}
    </div>
  );
}