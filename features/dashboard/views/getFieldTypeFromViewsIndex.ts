/**
 * Auto-generated field type mapping from views index
 * This file is generated by index-to-view/generate-field-views.ts
 * DO NOT EDIT THIS FILE MANUALLY
 */

/**
 * Get the field type from a field's viewsIndex
 * @param viewsIndex The views index of the field
 * @returns The field type name
 */
export function getFieldTypeFromViewsIndex(viewsIndex: number): string {
  const viewsIndexToType: Record<number, string> = {
    0: "id",
    1: "text",
    2: "password",
    3: "relationship",
    4: "timestamp",
    5: "checkbox",
    6: "virtual",
    7: "select",
    8: "json",
    9: "float",
    10: "integer"
  };

  const fieldType = viewsIndexToType[viewsIndex];
  if (!fieldType) {
    throw new Error(`Invalid views index: ${viewsIndex}`);
  }

  return fieldType;
}