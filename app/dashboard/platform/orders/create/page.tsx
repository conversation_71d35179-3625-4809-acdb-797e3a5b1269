import { CreateOrderFromScratch } from "@/features/platform/orders/components/CreateOrderFromScratch";
import { PageBreadcrumbs } from "@/features/dashboard/components/PageBreadcrumbs";

export default function CreateOrderPage() {
  return (
    <section
      aria-label="Create order"
      className="overflow-hidden flex flex-col"
    >
      <PageBreadcrumbs
        items={[
          {
            type: "link",
            label: "Dashboard",
            href: "/",
          },
          {
            type: "link",
            label: "Platform",
            href: "/dashboard/platform",
          },
          {
            type: "link",
            label: "Orders",
            href: "/dashboard/platform/orders",
          },
          {
            type: "page",
            label: "Create",
          },
        ]}
      />

      <div className="flex-1 overflow-auto">
        <div className="max-w-4xl mx-auto p-6">
          <CreateOrderFromScratch />
        </div>
      </div>
    </section>
  );
}