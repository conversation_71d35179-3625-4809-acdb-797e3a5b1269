import { createShopWebhook as executeCreateShopWebhook } from "../../utils/shopProviderAdapter";

interface CreateShopWebhookArgs {
  shopId: string;
  topic: string;
  endpoint: string;
}

async function createShopWebhook(
  root: any,
  { shopId, topic, endpoint }: CreateShopWebhookArgs,
  context: any
) {
  try {
    // Fetch the shop using the provided shopId
    const shop = await context.query.Shop.findOne({
      where: { id: shopId },
      query: "id domain accessToken platform { id createWebhookFunction }",
    });

    if (!shop) {
      return { success: false, error: "Shop not found" };
    }

    if (!shop.platform) {
      return { success: false, error: "Platform configuration not specified." };
    }

    const result = await executeCreateShopWebhook({
      platform: {
        ...shop.platform,
        domain: shop.domain,
        accessToken: shop.accessToken,
      },
      endpoint,
      events: [topic],
    });

    return { success: true, webhookId: result.webhookId };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
}

export default createShopWebhook;