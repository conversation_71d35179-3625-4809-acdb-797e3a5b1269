"use client";
import React, { useMemo, useState } from "react";
import { useCreateItem } from "@/features/dashboard/hooks/useCreateItem";
import { useAdminMeta } from "@/features/dashboard/hooks/useAdminMeta";
import { Button } from "@/components/ui/button";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Fields } from "@/features/dashboard/components/Fields";
import { GraphQLErrorNotice } from "@/features/dashboard/components/GraphQLErrorNotice";

export function getFilteredProps(props: any, modifications: any[], defaultCollapse?: boolean) {
  const fieldKeysToShow = modifications.map((mod) => mod.key);
  const breakGroups = modifications.reduce((acc: string[], mod) => {
    if (mod.breakGroup) {
      acc.push(mod.breakGroup);
    }
    return acc;
  }, []);

  const newFieldModes = { ...props.fieldModes };

  Object.keys(props.fields).forEach((key) => {
    if (!fieldKeysToShow.includes(key)) {
      newFieldModes[key] = "hidden";
    } else {
      newFieldModes[key] = props.fieldModes[key] || "edit";
    }
  });

  const updatedFields = Object.keys(props.fields).reduce((obj: any, key) => {
    const modification = modifications.find((mod) => mod.key === key);
    if (modification) {
      obj[key] = {
        ...props.fields[key],
        fieldMeta: {
          ...props.fields[key].fieldMeta,
          ...modification.fieldMeta,
        },
      };
    } else {
      obj[key] = props.fields[key];
    }
    return obj;
  }, {});

  const reorderedFields = modifications.reduce((obj: any, mod) => {
    obj[mod.key] = updatedFields[mod.key];
    return obj;
  }, {});

  const updatedGroups = props.groups.map((group: any) => {
    if (breakGroups.includes(group.label)) {
      return {
        ...group,
        fields: group.fields.filter(
          (field: any) => !fieldKeysToShow.includes(field.path)
        ),
      };
    }
    return {
      ...group,
      collapsed: defaultCollapse,
    };
  });

  return {
    ...props,
    fields: reorderedFields,
    fieldModes: newFieldModes,
    groups: updatedGroups,
  };
}

export function CreateShop({ 
  trigger, 
  platforms, 
  onShopCreated 
}: { 
  trigger: React.ReactNode;
  platforms?: any[];
  onShopCreated?: () => void;
}) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const { adminMeta } = useAdminMeta();
  const list = adminMeta.lists.Shop;
  const { create, props, state, error } = useCreateItem(list);

  const handleShopCreation = async () => {
    const item = await create();
    if (item) {
      setIsDialogOpen(false);
      if (onShopCreated) {
        onShopCreated();
      } else {
        // Refresh the page to show the new shop
        window.location.reload();
      }
    }
  };

  const handleDialogClose = () => {
    setIsDialogOpen(false);
  };

  const platformId = props.value.platform?.value?.value?.id;

  const filteredProps = useMemo(() => {
    const modifications = [
      { key: "platform", fieldMeta: { hideButtons: true } },
    ];
    return getFilteredProps(props, modifications);
  }, [props]);

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>

      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create Shop</DialogTitle>
          <DialogDescription>
            Connect a new shop to your platform
          </DialogDescription>
        </DialogHeader>

        {error && (
          <GraphQLErrorNotice
            networkError={error?.networkError}
            errors={error?.graphQLErrors}
          />
        )}
        <Fields {...filteredProps} />

        {platformId && <FilteredFields platformId={platformId} props={props} />}

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleDialogClose}
          >
            Cancel
          </Button>
          <Button
            disabled={state === "loading"}
            onClick={handleShopCreation}
          >
            {state === "loading" ? "Creating..." : "Create Shop"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export function FilteredFields({ platformId, props }: { platformId: string; props: any }) {
  // For now, just show the basic fields since we don't have platform details query
  const modifications = [
    { key: "name" },
    { key: "domain", breakGroup: "Credentials" },
    { key: "accessToken", breakGroup: "Credentials" },
  ];

  const filteredProps = getFilteredProps(props, modifications);

  if (!filteredProps.fields) return null;

  return (
    <div className="bg-muted/20 p-4 border rounded-lg overflow-auto max-h-[50vh]">
      <Fields {...filteredProps} />
    </div>
  );
}