{"name": "weekday-keystone", "version": "0.1.0", "private": true, "scripts": {"dev": "keystone build --no-ui && npm run migrate && next dev --turbopack", "build": "keystone build --no-ui && npm run migrate && next build", "start": "next start", "lint": "next lint", "migrate:gen": "keystone build --no-ui && tsx features/dashboard/views/index-to-view && keystone prisma migrate dev", "migrate": "prisma migrate deploy"}, "dependencies": {"@graphql-tools/schema": "^10.0.23", "@keystone-6/auth": "^8.1.0", "@keystone-6/core": "^6.3.1", "@keystone-6/document-renderer": "^1.1.2", "@keystone-6/fields-document": "^9.1.1", "@prisma/client": "5.22.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@react-aria/overlays": "^3.27.1", "@remixicon/react": "^4.6.0", "@tanstack/react-table": "^8.21.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "dumb-passwords": "^0.2.1", "graphql": "^16.11.0", "graphql-request": "^5.0.0", "graphql-upload": "^15.0.2", "graphql-yoga": "^3.1.0", "lucide-react": "^0.511.0", "next": "15.3.2", "next-themes": "^0.4.6", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.1", "react-sortablejs": "^6.1.4", "sonner": "^2.0.3", "sortablejs": "^1.15.6", "swr": "^2.3.3", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.40.0", "@tailwindcss/postcss": "^4", "@types/graphql-upload": "^17.0.0", "@types/node": "^20.14.10", "@types/node-fetch": "^2.6.12", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "node-fetch": "^3.3.2", "prisma": "5.22.0", "tailwindcss": "^4", "tsx": "^4.19.4", "tw-animate-css": "^1.3.2", "typescript": "^5"}, "overrides": {"react": "^19.0.0", "react-dom": "^19.0.0"}}