"use client";

import React, { useState, useEffect, useMemo } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Search, 
  Plus, 
  Minus, 
  ShoppingCart, 
  Package, 
  DollarSign,
  AlertCircle,
  Check,
  X
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

interface CartItem {
  id: string;
  title: string;
  sku?: string;
  price?: number;
  image?: string;
  inventory?: number;
  available: boolean;
  channelId: string;
  channelName: string;
  productId: string;
  variantId?: string;
}

interface CartItemSelectProps {
  channels: Array<{
    id: string;
    name: string;
    platform: { name: string };
  }>;
  onSelect: (item: CartItem, quantity: number) => void;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export function CartItemSelect({ 
  channels, 
  onSelect, 
  isOpen, 
  onOpenChange 
}: CartItemSelectProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedChannel, setSelectedChannel] = useState<string>("");
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedItems, setSelectedItems] = useState<Map<string, number>>(new Map());
  const { toast } = useToast();

  // Mock data for demonstration - in real implementation, this would fetch from API
  const mockCartItems: CartItem[] = [
    {
      id: "item1",
      title: "Wireless Bluetooth Headphones",
      sku: "WBH-001",
      price: 79.99,
      image: "/api/placeholder/64/64",
      inventory: 25,
      available: true,
      channelId: "channel1",
      channelName: "Amazon",
      productId: "prod1",
      variantId: "var1"
    },
    {
      id: "item2", 
      title: "USB-C Cable 6ft",
      sku: "USC-006",
      price: 12.99,
      image: "/api/placeholder/64/64",
      inventory: 150,
      available: true,
      channelId: "channel1",
      channelName: "Amazon",
      productId: "prod2"
    },
    {
      id: "item3",
      title: "Phone Case - Clear",
      sku: "PC-CLR",
      price: 24.99,
      image: "/api/placeholder/64/64",
      inventory: 0,
      available: false,
      channelId: "channel2",
      channelName: "eBay",
      productId: "prod3",
      variantId: "var2"
    },
    {
      id: "item4",
      title: "Portable Charger 10000mAh",
      sku: "PCH-10K",
      price: 35.99,
      image: "/api/placeholder/64/64",
      inventory: 8,
      available: true,
      channelId: "channel2",
      channelName: "eBay",
      productId: "prod4"
    }
  ];

  // Filter items based on search and selected channel
  const filteredItems = useMemo(() => {
    return mockCartItems.filter(item => {
      const matchesSearch = searchTerm === "" || 
        item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.sku?.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesChannel = selectedChannel === "" || item.channelId === selectedChannel;
      
      return matchesSearch && matchesChannel;
    });
  }, [searchTerm, selectedChannel]);

  const updateQuantity = (itemId: string, delta: number) => {
    setSelectedItems(prev => {
      const newMap = new Map(prev);
      const currentQty = newMap.get(itemId) || 0;
      const newQty = Math.max(0, currentQty + delta);
      
      if (newQty === 0) {
        newMap.delete(itemId);
      } else {
        newMap.set(itemId, newQty);
      }
      
      return newMap;
    });
  };

  const handleAddSelected = () => {
    const itemsToAdd: Array<{ item: CartItem; quantity: number }> = [];
    
    selectedItems.forEach((quantity, itemId) => {
      const item = filteredItems.find(i => i.id === itemId);
      if (item && quantity > 0) {
        itemsToAdd.push({ item, quantity });
      }
    });

    if (itemsToAdd.length === 0) {
      toast({
        title: "No items selected",
        description: "Please select at least one item with quantity > 0",
        variant: "destructive"
      });
      return;
    }

    // Validate availability
    const unavailableItems = itemsToAdd.filter(({ item, quantity }) => 
      !item.available || (item.inventory !== undefined && quantity > item.inventory)
    );

    if (unavailableItems.length > 0) {
      toast({
        title: "Availability issue",
        description: `Some items are not available or exceed inventory limits`,
        variant: "destructive"
      });
      return;
    }

    // Add all selected items
    itemsToAdd.forEach(({ item, quantity }) => {
      onSelect(item, quantity);
    });

    toast({
      title: "Success",
      description: `Added ${itemsToAdd.length} item(s) to cart`,
    });

    // Clear selections and close dialog
    setSelectedItems(new Map());
    onOpenChange(false);
  };

  const handleClearAll = () => {
    setSelectedItems(new Map());
  };

  const totalSelectedItems = Array.from(selectedItems.values()).reduce((sum, qty) => sum + qty, 0);

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ShoppingCart className="h-5 w-5" />
            Select Cart Items
          </DialogTitle>
          <DialogDescription>
            Search and select products from your connected channels to add to the cart
          </DialogDescription>
        </DialogHeader>

        {/* Search and Filter Controls */}
        <div className="space-y-4">
          <div className="flex gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by product name or SKU..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9"
              />
            </div>
            <Select value={selectedChannel} onValueChange={setSelectedChannel}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="All channels" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All channels</SelectItem>
                {channels.map((channel) => (
                  <SelectItem key={channel.id} value={channel.id}>
                    {channel.name} ({channel.platform.name})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {totalSelectedItems > 0 && (
            <div className="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg border">
              <div className="flex items-center gap-2">
                <Package className="h-4 w-4" />
                <span className="font-medium">{totalSelectedItems} items selected</span>
              </div>
              <Button variant="outline" size="sm" onClick={handleClearAll}>
                Clear all
              </Button>
            </div>
          )}
        </div>

        {/* Items List */}
        <div className="flex-1 overflow-auto space-y-2">
          {filteredItems.length === 0 ? (
            <div className="text-center py-8">
              <Package className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <p className="text-muted-foreground">
                {searchTerm || selectedChannel ? "No items match your filters" : "No cart items available"}
              </p>
            </div>
          ) : (
            filteredItems.map((item) => {
              const selectedQty = selectedItems.get(item.id) || 0;
              const isOutOfStock = !item.available || (item.inventory !== undefined && item.inventory === 0);
              const exceedsInventory = item.inventory !== undefined && selectedQty > item.inventory;

              return (
                <Card key={item.id} className={`transition-colors ${!item.available ? 'opacity-60' : ''}`}>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-4">
                      {/* Product Image */}
                      <div className="w-16 h-16 bg-muted rounded-lg flex items-center justify-center">
                        {item.image ? (
                          <img
                            src={item.image}
                            alt={item.title}
                            className="w-full h-full object-cover rounded-lg"
                          />
                        ) : (
                          <Package className="h-6 w-6 text-muted-foreground" />
                        )}
                      </div>

                      {/* Product Info */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-medium truncate">{item.title}</h4>
                            <div className="flex items-center gap-2 mt-1">
                              {item.sku && (
                                <Badge variant="outline" className="text-xs">
                                  {item.sku}
                                </Badge>
                              )}
                              <Badge variant="secondary" className="text-xs">
                                {item.channelName}
                              </Badge>
                            </div>
                          </div>
                          
                          <div className="text-right">
                            {item.price && (
                              <div className="flex items-center gap-1 font-medium">
                                <DollarSign className="h-3 w-3" />
                                {item.price.toFixed(2)}
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Availability Info */}
                        <div className="flex items-center gap-2 mt-2">
                          {isOutOfStock ? (
                            <div className="flex items-center gap-1 text-red-600">
                              <X className="h-3 w-3" />
                              <span className="text-xs">Out of stock</span>
                            </div>
                          ) : (
                            <div className="flex items-center gap-1 text-green-600">
                              <Check className="h-3 w-3" />
                              <span className="text-xs">
                                {item.inventory !== undefined ? `${item.inventory} in stock` : 'Available'}
                              </span>
                            </div>
                          )}
                          
                          {exceedsInventory && (
                            <div className="flex items-center gap-1 text-orange-600">
                              <AlertCircle className="h-3 w-3" />
                              <span className="text-xs">Exceeds inventory</span>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Quantity Controls */}
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="icon"
                          className="h-8 w-8"
                          onClick={() => updateQuantity(item.id, -1)}
                          disabled={selectedQty === 0}
                        >
                          <Minus className="h-3 w-3" />
                        </Button>
                        
                        <div className="w-12 text-center">
                          <span className="font-medium">{selectedQty}</span>
                        </div>
                        
                        <Button
                          variant="outline"
                          size="icon"
                          className="h-8 w-8"
                          onClick={() => updateQuantity(item.id, 1)}
                          disabled={isOutOfStock || (item.inventory !== undefined && selectedQty >= item.inventory)}
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleAddSelected}
            disabled={totalSelectedItems === 0}
          >
            Add {totalSelectedItems > 0 ? `${totalSelectedItems} ` : ''}Items to Cart
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}