"use client";

import { useRouter, useSearchParams, usePathname } from "next/navigation";
import { useRef, useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { EllipsisVertical, Plus } from "lucide-react";

interface Platform {
  id: string;
  name: string;
  count?: number;
}

interface PlatformTabsProps {
  platforms: Platform[];
  totalCount: number;
  onAddPlatform?: () => void;
  onEditPlatform?: (platformId: string) => void;
  renderAddButton?: () => React.ReactNode;
  renderEditButton?: (platform: Platform) => React.ReactNode;
}

export function PlatformTabs({ 
  platforms, 
  totalCount,
  onAddPlatform,
  onEditPlatform,
  renderAddButton,
  renderEditButton
}: PlatformTabsProps) {
  const router = useRouter();
  const searchParams = useSearchParams()!;
  const pathname = usePathname();
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const [, updateScroll] = useState(0);

  const tabRefs = useRef<Array<HTMLDivElement | null>>([]);
  const scrollContainerRef = useRef<HTMLDivElement | null>(null);

  // Get current platform from URL
  const currentPlatform = searchParams.get("platform") || "all";

  const handlePlatformChange = (platformId: string) => {
    const params = new URLSearchParams(searchParams.toString());
    if (platformId === "all") {
      params.delete("platform");
    } else {
      params.set("platform", platformId);
    }
    params.set("page", "1"); // Reset to first page
    router.push(`${pathname}?${params.toString()}`);
  };

  const handleScroll = () => {
    updateScroll(n => n + 1);
  };

  const handleAddPlatform = () => {
    if (onAddPlatform) {
      onAddPlatform();
    }
  };

  const handleEditPlatform = (e: React.MouseEvent, platformId: string) => {
    e.stopPropagation();
    if (onEditPlatform) {
      onEditPlatform(platformId);
    }
  };

  const activeIndex = currentPlatform === "all" ? 0 : platforms.findIndex((p) => p.id === currentPlatform) + 1;
  const activeTabOffsetLeft = tabRefs.current[activeIndex]?.offsetLeft || 0;
  const activeTabWidth = tabRefs.current[activeIndex]?.offsetWidth || 0;
  const scrollOffset = scrollContainerRef.current ? scrollContainerRef.current.scrollLeft : 0;

  return (
    <div className="relative">
      <div
        className="absolute h-[28px] mt-1 transition-all duration-300 ease-out bg-muted/60 rounded-[6px] flex items-center ml-4 md:ml-6"
        style={{
          left: `${hoveredIndex !== null ? (tabRefs.current[hoveredIndex]?.offsetLeft || 0) - scrollOffset : 0}px`,
          width: `${hoveredIndex !== null ? tabRefs.current[hoveredIndex]?.offsetWidth || 0 : 0}px`,
          opacity: hoveredIndex !== null ? 1 : 0,
        }}
      />

      <div
        className="absolute bottom-[-1px] h-[2px] bg-foreground transition-all duration-300 ease-out ml-4 md:ml-6"
        style={{
          left: `${activeTabOffsetLeft - scrollOffset}px`,
          width: `${activeTabWidth}px`,
        }}
      />

      <div ref={scrollContainerRef} onScroll={handleScroll} className="w-full overflow-x-auto no-scrollbar px-4 md:px-6">
        <div className="relative flex space-x-[6px] items-center pb-1">
          {/* All Platforms Tab */}
          <div
            ref={el => { tabRefs.current[0] = el }}
            className={`relative flex items-center gap-2 px-3 py-1.5 text-sm font-medium cursor-pointer transition-colors duration-200 rounded-[6px] whitespace-nowrap ${
              currentPlatform === "all"
                ? "text-foreground"
                : "text-muted-foreground hover:text-foreground"
            }`}
            onClick={() => handlePlatformChange("all")}
            onMouseEnter={() => setHoveredIndex(0)}
            onMouseLeave={() => setHoveredIndex(null)}
          >
            All Platforms
            <Badge variant="secondary" className="text-xs">
              {totalCount}
            </Badge>
          </div>

          {/* Individual Platform Tabs */}
          {platforms.map((platform, index) => (
            <div
              key={platform.id}
              ref={el => { tabRefs.current[index + 1] = el }}
              className={`relative flex items-center gap-2 px-3 py-1.5 text-sm font-medium cursor-pointer transition-colors duration-200 rounded-[6px] whitespace-nowrap ${
                currentPlatform === platform.id
                  ? "text-foreground"
                  : "text-muted-foreground hover:text-foreground"
              }`}
              onClick={() => handlePlatformChange(platform.id)}
              onMouseEnter={() => setHoveredIndex(index + 1)}
              onMouseLeave={() => setHoveredIndex(null)}
            >
              {platform.name}
              <Badge variant="secondary" className="text-xs">
                {platform.count || 0}
              </Badge>
              
              {renderEditButton ? (
                renderEditButton(platform)
              ) : (
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={(e) => handleEditPlatform(e, platform.id)}
                >
                  <EllipsisVertical className="h-3 w-3" />
                </Button>
              )}
            </div>
          ))}

          {/* Add Platform Button */}
          <div className="flex items-center ml-2">
            {renderAddButton ? (
              renderAddButton()
            ) : (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleAddPlatform}
                className="text-muted-foreground hover:text-foreground"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Platform
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
