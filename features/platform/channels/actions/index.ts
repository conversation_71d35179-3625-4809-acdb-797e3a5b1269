'use server';

import { revalidatePath } from 'next/cache';
import { keystoneClient } from "../../../dashboard/lib/keystoneClient";

// Interface for channel data (exported for potential use in other files)
export interface Channel {
  id: string;
  title: string;
  [key: string]: unknown;
}

/**
 * Get list of channels
 */
export async function getChannels(
  where: Record<string, unknown> = {},
  take: number = 10,
  skip: number = 0,
  orderBy: Array<Record<string, string>> = [{ createdAt: 'desc' }],
  selectedFields: string = `
    id name domain accessToken metadata createdAt updatedAt platform { id name } user { id name email } channelItems { id } links { id }
  `
) {
  const query = `
    query GetChannels($where: ChannelWhereInput, $take: Int!, $skip: Int!, $orderBy: [ChannelOrderByInput!]) {
      items: channels(where: $where, take: $take, skip: $skip, orderBy: $orderBy) {
        ${selectedFields}
      }
      count: channelsCount(where: $where)
    }
  `;

  const response = await keystoneClient(query, {
    where,
    take,
    skip,
    orderBy,
  });

  if (response.success) {
    return {
      success: true,
      data: {
        items: response.data.items || [],
        count: response.data.count || 0,
      },
    };
  } else {
    console.error('Error fetching channels:', response.error);
    return {
      success: false,
      error: response.error || 'Failed to fetch channels',
      data: { items: [], count: 0 },
    };
  }
}

/**
 * Get filtered channels with search and pagination
 */
export async function getFilteredChannels(
  status?: string,
  search?: string,
  page: number = 1,
  pageSize: number = 10,
  sort?: string
) {
  // Build where clause
  const where: Record<string, any> = {};
  
  // Status filtering
  if (status && status !== 'all') {
    where.status = { equals: status };
  }
  
  // Search filtering (adjust fields as needed)
  if (search?.trim()) {
    where.OR = [
      { title: { contains: search, mode: 'insensitive' } },
      // Add more searchable fields as needed
    ];
  }

  // Build orderBy clause
  let orderBy: Array<Record<string, string>> = [{ createdAt: 'desc' }];
  if (sort) {
    if (sort.startsWith('-')) {
      const field = sort.substring(1);
      orderBy = [{ [field]: 'desc' }];
    } else {
      orderBy = [{ [sort]: 'asc' }];
    }
  }

  // Calculate pagination
  const skip = (page - 1) * pageSize;

  try {
    const result = await getChannels(where, pageSize, skip, orderBy);
    return result;
  } catch (error: any) {
    console.error('Error in getFilteredChannels:', error);
    return {
      success: false,
      error: error.message || 'Failed to get filtered channels',
      data: { items: [], count: 0 },
    };
  }
}

/**
 * Get a single channel by ID
 */
export async function getChannel(id: string) {
  const query = `
    query GetChannel($id: ID!) {
      channel(where: { id: $id }) {
        id name domain accessToken metadata createdAt updatedAt platform { id name } user { id name email } channelItems { id } links { id }
      }
    }
  `;

  const response = await keystoneClient(query, { id });

  if (response.success) {
    if (!response.data.channel) {
      return {
        success: false,
        error: 'Channel not found',
        data: null,
      };
    }

    return {
      success: true,
      data: response.data.channel,
    };
  } else {
    console.error('Error fetching channel:', response.error);
    return {
      success: false,
      error: response.error || 'Failed to fetch channel',
      data: null,
    };
  }
}

/**
 * Get channel status counts for StatusTabs
 */
export async function getChannelStatusCounts() {
  const statusKeys = ["active","inactive"];
  
  const statusQueries = statusKeys.map(status => 
    `${status}: channelsCount(where: { status: { equals: "${status}" } })`
  ).join('\n      ');
  
  const query = `
    query GetChannelStatusCounts {
      ${statusQueries}
      all: channelsCount
    }
  `;

  const response = await keystoneClient(query);

  if (response.success) {
    const counts: Record<string, number> = {
      all: response.data.all || 0,
    };
    
    statusKeys.forEach(status => {
      counts[status] = response.data[status] || 0;
    });
    
    return {
      success: true,
      data: counts,
    };
  } else {
    console.error('Error fetching channel status counts:', response.error);
    const emptyCounts: Record<string, number> = {
      all: 0,
    };
    
    statusKeys.forEach(status => {
      emptyCounts[status] = 0;
    });
    
    return {
      success: false,
      error: response.error || 'Failed to fetch channel status counts',
      data: emptyCounts,
    };
  }
}

/**
 * Update channel status
 */
export async function updateChannelStatus(id: string, status: string) {
  const mutation = `
    mutation UpdateChannelStatus($id: ID!, $data: ChannelUpdateInput!) {
      updateChannel(where: { id: $id }, data: $data) {
        id
        status
      }
    }
  `;

  const response = await keystoneClient(mutation, {
    id,
    data: { status },
  });

  if (response.success) {
    // Revalidate the channel page to reflect the status change
    revalidatePath(`/dashboard/platform/channels/${id}`);
    revalidatePath('/dashboard/platform/channels');

    return {
      success: true,
      data: response.data.updateChannel,
    };
  } else {
    console.error('Error updating channel status:', response.error);
    return {
      success: false,
      error: response.error || 'Failed to update channel status',
      data: null,
    };
  }
}

/**
 * Get list of channel platforms
 */
export async function getChannelPlatforms() {
  const query = `
    query GetChannelPlatforms {
      channelPlatforms {
        id
        name
        appKey
        appSecret
        oAuthFunction
        oAuthCallbackFunction
        createdAt
        updatedAt
        channels {
          id
        }
      }
    }
  `;

  const response = await keystoneClient(query, {});
  return response;
}

/**
 * Get filtered channels with platform filter
 */
export async function getFilteredChannelsWithPlatform(
  search: string | null = null,
  platformId: string | null = null,
  page: number = 1,
  pageSize: number = 10,
  sort: { field: string; direction: 'ASC' | 'DESC' } | null = null
) {
  const where: Record<string, unknown> = {};

  // Add platform filter if provided
  if (platformId) {
    where.platform = { id: { equals: platformId } };
  }

  // Add search filter if provided
  if (search) {
    const searchConditions = [
      { name: { contains: search, mode: 'insensitive' } },
      { domain: { contains: search, mode: 'insensitive' } },
      { user: { name: { contains: search, mode: 'insensitive' } } },
      { user: { email: { contains: search, mode: 'insensitive' } } },
    ];

    if (platformId) {
      // If platform filter is active, combine it with search
      where.AND = [
        { platform: { id: { equals: platformId } } },
        { OR: searchConditions }
      ];
    } else {
      // If no platform filter, include platform name in search
      searchConditions.push({ platform: { name: { contains: search, mode: 'insensitive' } } });
      where.OR = searchConditions;
    }
  }

  // Calculate pagination
  const skip = (page - 1) * pageSize;

  // Handle sorting
  const orderBy = sort
    ? [{ [sort.field]: sort.direction.toLowerCase() }]
    : [{ createdAt: 'desc' }];

  return getChannels(where, pageSize, skip, orderBy);
}

/**
 * Create channel platform
 */
export async function createChannelPlatform(data: Record<string, unknown>) {
  const query = `
    mutation CreateChannelPlatform($data: ChannelPlatformCreateInput!) {
      createChannelPlatform(data: $data) {
        id
        name
      }
    }
  `;

  const response = await keystoneClient(query, { data });

  if (response.success) {
    revalidatePath('/dashboard/platform/channels');
  }

  return response;
}

/**
 * Create channel
 */
export async function createChannel(data: {
  name: string;
  domain?: string;
  accessToken?: string;
  platformId: string;
  metadata?: any;
}) {
  const query = `
    mutation CreateChannel($data: ChannelCreateInput!) {
      createChannel(data: $data) {
        id
        name
        domain
        platform {
          id
          name
        }
        createdAt
      }
    }
  `;

  const channelData = {
    name: data.name,
    domain: data.domain,
    accessToken: data.accessToken,
    platform: { connect: { id: data.platformId } },
    metadata: data.metadata || {},
  };

  const response = await keystoneClient(query, { data: channelData });

  if (response.success) {
    revalidatePath('/dashboard/platform/channels');
  }

  return response;
}
