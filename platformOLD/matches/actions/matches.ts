'use server';

import { revalidatePath } from 'next/cache';
import { keystoneClient } from "@/features/dashboard/lib/keystoneClient";

// Interface for match data
interface Match {
  id: string;
  input?: Record<string, unknown>;
  output?: Record<string, unknown>;
  createdAt: string;
  updatedAt?: string;
}

/**
 * Get list of matches
 */
export async function getMatches(
  where: Record<string, unknown> = {},
  take: number = 10,
  skip: number = 0,
  orderBy: Array<Record<string, string>> = [{ createdAt: 'desc' }],
  selectedFields: string = `
    id
    outputPriceChanged
    inventoryNeedsToBeSynced
    input {
      id
      productId
      variantId
      lineItemId
      quantity
      externalDetails
      shop {
        id
        name
        domain
      }
    }
    output {
      id
      productId
      variantId
      lineItemId
      quantity
      price
      priceChanged
      externalDetails
      channel {
        id
        name
        domain
      }
    }
    user {
      id
      name
      email
    }
    createdAt
    updatedAt
  `
) {
  const query = `
    query GetMatches($where: MatchWhereInput, $take: Int!, $skip: Int!, $orderBy: [MatchOrderByInput!]) {
      items: matches(where: $where, take: $take, skip: $skip, orderBy: $orderBy) {
        ${selectedFields}
      }
      count: matchesCount(where: $where)
    }
  `;

  const response = await keystoneClient(query, { where, take, skip, orderBy });
  return response;
}

/**
 * Get filtered matches based on search parameters
 */
export async function getFilteredMatches(
  search: string | null = null,
  page: number = 1,
  pageSize: number = 10,
  sort: { field: string; direction: 'ASC' | 'DESC' } | null = null
) {
  const where: Record<string, unknown> = {};

  // Add search filter if provided
  if (search) {
    where.OR = [
      { id: { contains: search, mode: 'insensitive' } },
    ];
  }

  // Calculate pagination
  const skip = (page - 1) * pageSize;

  // Handle sorting
  const orderBy = sort
    ? [{ [sort.field]: sort.direction.toLowerCase() }]
    : [{ createdAt: 'desc' }];

  return getMatches(where, pageSize, skip, orderBy);
}

/**
 * Sync inventory for matches
 */
export async function syncInventory(ids: string[]) {
  const query = `
    mutation SyncInventory($ids: [ID!]!) {
      syncInventory(ids: $ids) {
        id
      }
    }
  `;
  const response = await keystoneClient(query, { ids });
  if (response.success) {
    revalidatePath(`/dashboard/platform/matches`);
  }
  return response;
}

/**
 * Create a new match
 */
export async function createMatch(data: Record<string, unknown>) {
  const query = `
    mutation CreateMatch($data: MatchCreateInput!) {
      createMatch(data: $data) {
        id
        input
        output
        createdAt
      }
    }
  `;
  const response = await keystoneClient(query, { data });
  if (response.success) {
    revalidatePath(`/dashboard/platform/matches`);
  }
  return response;
}


/**
 * Update a match
 */
export async function updateMatch(id: string, data: Record<string, unknown>) {
  const query = `
    mutation UpdateMatch($where: MatchWhereUniqueInput!, $data: MatchUpdateInput!) {
      updateMatch(where: $where, data: $data) {
        id
        input
        output
        updatedAt
      }
    }
  `;
  const response = await keystoneClient(query, { where: { id }, data });
  if (response.success) {
    revalidatePath(`/dashboard/platform/matches`);
  }
  return response;
}

/**
 * Delete a match
 */
export async function deleteMatch(id: string) {
  const query = `
    mutation DeleteMatch($where: MatchWhereUniqueInput!) {
      deleteMatch(where: $where) {
        id
      }
    }
  `;
  const response = await keystoneClient(query, { where: { id } });
  if (response.success) {
    revalidatePath(`/dashboard/platform/matches`);
  }
  return response;
}

/**
 * Search shop products
 */
export async function searchShopProducts(shopId: string, searchEntry: string = "", after?: string) {
  const query = `
    query SearchShopProducts($shopId: String!, $searchEntry: String!, $after: String) {
      searchShopProducts(shopId: $shopId, searchEntry: $searchEntry, after: $after) {
        products {
          image
          title
          productId
          variantId
          price
          availableForSale
          inventory
          inventoryTracked
          productLink
          cursor
        }
        pageInfo {
          hasNextPage
          hasPreviousPage
          startCursor
          endCursor
        }
      }
    }
  `;
  const response = await keystoneClient(query, { shopId, searchEntry, after });
  return response;
}

/**
 * Search channel products
 */
export async function searchChannelProducts(channelId: string, searchEntry: string = "", after?: string) {
  const query = `
    query SearchChannelProducts($channelId: String!, $searchEntry: String!, $after: String) {
      searchChannelProducts(channelId: $channelId, searchEntry: $searchEntry, after: $after) {
        products {
          image
          title
          productId
          variantId
          price
          availableForSale
          inventory
          inventoryTracked
          productLink
          cursor
        }
        pageInfo {
          hasNextPage
          hasPreviousPage
          startCursor
          endCursor
        }
      }
    }
  `;
  const response = await keystoneClient(query, { channelId, searchEntry, after });
  return response;
}

