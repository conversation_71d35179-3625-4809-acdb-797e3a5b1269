"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Loader2, Plus, Info, CircleAlert } from "lucide-react";
import { 
  getShopWebhooks, 
  createShopWebhook, 
  deleteShopWebhook 
} from "../actions/webhooks";


const RECOMMENDED_WEBHOOKS = [
  {
    callbackUrl: "/api/handlers/shop/create-order/[shopId]",
    topic: "ORDER_CREATED",
    description:
      "When an order is created on this shop, Openship will create the order to be fulfilled.",
  },
  {
    callbackUrl: "/api/handlers/shop/cancel-order/[shopId]",
    topic: "ORDER_CANCELLED",
    description:
      "When an order is cancelled on this shop, Openship will mark the order status cancelled",
  },
  {
    callbackUrl: "/api/handlers/shop/cancel-order/[shopId]",
    topic: "ORDER_CHARGEBACKED",
    description:
      "When an order is chargebacked on this shop, Openship will mark the order status cancelled",
  },
];

const WebhookItem = ({ webhook, onRefresh, shopId }: {
  webhook: any;
  onRefresh: () => void;
  shopId: string;
}) => {
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const handleDelete = async () => {
    setLoading(true);
    try {
      const response = await deleteShopWebhook(shopId, webhook.id);

      if (response.success) {
        toast({
          title: "Webhook deleted successfully",
        });
        onRefresh();
      } else {
        toast({
          title: "Failed to delete webhook",
          description: response.error || "Unknown error",
          variant: "destructive",
        });
      }
    } catch (err: any) {
      toast({
        title: "Failed to delete webhook",
        description: err.message,
        variant: "destructive",
      });
    }
    setLoading(false);
  };

  return (
    <div className="flex flex-col gap-1">
      <div className="flex items-center justify-between">
        <span className="text-xs font-medium">{webhook.topic}</span>
        <Button
          variant="outline"
          size="sm"
          className="h-6 px-2 text-xs text-red-600 border-red-200 hover:bg-red-50"
          onClick={handleDelete}
          disabled={loading}
        >
          {loading ? "Deleting..." : "Delete"}
        </Button>
      </div>
      <div className="flex flex-col gap-0.5 bg-background border mb-2 p-2 rounded-lg text-xs text-muted-foreground break-words">
        <span className="opacity-90 font-medium">Callback URL:</span>
        {webhook.callbackUrl}
      </div>
    </div>
  );
};

const RecommendedWebhookItem = ({ webhook, onRefresh, shopId }: {
  webhook: any;
  onRefresh: () => void;
  shopId: string;
}) => {
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const handleCreate = async () => {
    setLoading(true);
    try {
      const response = await createShopWebhook(
        shopId,
        webhook.topic,
        webhook.callbackUrl.replace("[shopId]", shopId)
      );

      if (response.success) {
        toast({
          title: "Webhook created successfully",
        });
        onRefresh();
      } else {
        toast({
          title: "Failed to create webhook",
          description: response.error || "Unknown error",
          variant: "destructive",
        });
      }
    } catch (err: any) {
      toast({
        title: "Failed to create webhook",
        description: err.message,
        variant: "destructive",
      });
    }
    setLoading(false);
  };

  return (
    <div className="flex flex-col gap-1">
      <div className="flex items-center justify-between">
        <div className="flex gap-2 items-center">
          <Button
            variant="secondary"
            size="icon"
            className="border h-5 w-5"
            onClick={handleCreate}
            disabled={loading}
          >
            {loading ? <Loader2 className="h-3 w-3 animate-spin" /> : <Plus className="h-3 w-3" />}
          </Button>
          <span className="text-xs font-medium">{webhook.topic}</span>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Info className="text-muted-foreground w-3 h-3" />
              </TooltipTrigger>
              <TooltipContent side="bottom" className="max-w-64">
                <p>{webhook.description}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>
    </div>
  );
};

export const Webhooks = ({ shopId }: { shopId: string }) => {
  const [webhooks, setWebhooks] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadWebhooks = async () => {
    try {
      setLoading(true);
      const response = await getShopWebhooks(shopId);
      if (response.success) {
        setWebhooks(response.data?.getShopWebhooks || []);
      } else {
        setError(response.error || "Failed to load webhooks");
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadWebhooks();
  }, [shopId]);

  if (loading) {
    return <div>Loading webhooks...</div>;
  }

  if (error) {
    return (
      <div className="rounded-md border border-red-500/50 px-4 py-3 text-red-600">
        <p className="text-sm">
          <CircleAlert
            className="me-3 -mt-0.5 inline-flex opacity-60"
            size={16}
            aria-hidden="true"
          />
          Error loading webhooks: {error}
        </p>
      </div>
    );
  }

  return (
    <div className="max-w-full w-80">
      <div className="flex flex-col gap-2">
        {webhooks.map((webhook: any) => (
          <WebhookItem
            key={webhook.id}
            webhook={webhook}
            onRefresh={loadWebhooks}
            shopId={shopId}
          />
        ))}
      </div>

      <div className="flex flex-col gap-2">
        {RECOMMENDED_WEBHOOKS.map((webhook) => {
          const existingWebhook = webhooks.find(
            (w: any) =>
              w.topic === webhook.topic &&
              w.callbackUrl === webhook.callbackUrl.replace("[shopId]", shopId)
          );
          return !existingWebhook ? (
            <RecommendedWebhookItem
              key={webhook.topic}
              webhook={webhook}
              onRefresh={loadWebhooks}
              shopId={shopId}
            />
          ) : null;
        })}
      </div>
    </div>
  );
};