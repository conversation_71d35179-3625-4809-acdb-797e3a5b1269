"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { DiamondPlus, ChevronDown, Plus, RotateCcw, X } from "lucide-react";
import Link from "next/link";
import { ShopOrderSearch } from "./ShopOrderSearch";

interface CreateOrderButtonProps {
  onCreateFromExisting?: () => void;
}

export function CreateOrderButton({ onCreateFromExisting }: CreateOrderButtonProps) {
  const [isCreateOrderDialogOpen, setIsCreateOrderDialogOpen] = useState(false);

  const handleCreateOrder = (type: "scratch" | "existing") => {
    if (type === "scratch") {
      // Navigate to create page
      window.location.href = "/dashboard/platform/orders/create";
    } else if (type === "existing") {
      // Open create from existing dialog
      if (onCreateFromExisting) {
        onCreateFromExisting();
      } else {
        setIsCreateOrderDialogOpen(true);
      }
    }
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button className="rounded-lg">
            <DiamondPlus className="mr-2" />
            Create Order
            <ChevronDown className="-me-1 ml-2 opacity-60" size={16} />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          <DropdownMenuItem onClick={() => handleCreateOrder("scratch")} className="cursor-pointer">
            <Plus className="h-4 w-4 mr-2" />
            From Scratch
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleCreateOrder("existing")} className="cursor-pointer">
            <RotateCcw className="h-4 w-4 mr-2" />
            From Existing
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Create From Existing Dialog */}
      <Dialog open={isCreateOrderDialogOpen} onOpenChange={setIsCreateOrderDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              Create Order from Existing Shop Order
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsCreateOrderDialogOpen(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </DialogTitle>
          </DialogHeader>
          <div className="overflow-y-auto">
            <ShopOrderSearch />
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}