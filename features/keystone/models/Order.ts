import { list } from "@keystone-6/core";
import { allOperations } from "@keystone-6/core/access";
import {
  checkbox,
  float,
  integer,
  json,
  relationship,
  text,
  timestamp,
} from "@keystone-6/core/fields";

import { isSignedIn, permissions, rules } from "../access";
import { trackingFields } from "./trackingFields";

export const Order = list({
  access: {
    operation: {
      create: isSignedIn,
      query: isSignedIn,
      update: isSignedIn,
      delete: permissions.canManageOrders,
    },
    filter: {
      query: rules.canReadOrders,
      update: rules.canManageOrders,
      delete: rules.canManageOrders,
    },
  },
  hooks: {
    resolveInput: {
      create: ({ operation, resolvedData, context }) => {
        // Auto-assign user if not provided
        if (!resolvedData.user && context.session?.itemId) {
          return {
            ...resolvedData,
            user: { connect: { id: context.session.itemId } },
          };
        }
        return resolvedData;
      },
    },
    // TODO: Add complex order processing hooks from OpenShip
    // afterOperation: async ({ operation, item, context }) => {
    //   // Order linking and processing logic
    // },
  },
  ui: {
    listView: {
      initialColumns: ["orderId", "orderName", "email", "totalPrice", "shop"],
    },
  },
  fields: {
    // Order identifiers
    orderId: text({
      isIndexed: "unique",
      validation: { isRequired: true },
    }),
    orderName: text(),
    email: text(),

    // Customer information
    first_name: text(),
    last_name: text(),
    streetAddress1: text(),
    streetAddress2: text(),
    city: text(),
    state: text(),
    zip: text(),
    country: text(),
    phone: text(),

    // Pricing
    currency: text(),
    totalPrice: float(),
    subTotalPrice: float(),
    totalDiscounts: float(),
    totalTax: float(),

    // Processing flags
    linkOrder: checkbox({ defaultValue: true }),
    matchOrder: checkbox({ defaultValue: true }),
    processOrder: checkbox({ defaultValue: true }),

    // Status tracking
    status: text({ defaultValue: "PENDING" }),
    error: text({
      ui: {
        displayMode: "textarea",
      },
    }),

    // Metadata
    orderMetadata: json(),

    // Relationships
    shop: relationship({
      ref: "Shop.orders",
      ui: {
        displayMode: "cards",
        cardFields: ["name", "domain"],
        inlineCreate: { fields: ["name", "domain"] },
        inlineEdit: { fields: ["name", "domain"] },
      },
    }),
    lineItems: relationship({
      ref: "LineItem.order",
      many: true,
      ui: {
        displayMode: "cards",
        cardFields: ["name", "quantity", "price"],
        inlineCreate: { fields: ["name", "quantity", "price"] },
        inlineEdit: { fields: ["name", "quantity", "price"] },
      },
    }),
    cartItems: relationship({
      ref: "CartItem.order",
      many: true,
    }),
    user: relationship({
      ref: "User.orders",
    }),

    ...trackingFields,
  },
});
