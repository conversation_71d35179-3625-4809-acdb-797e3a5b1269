// This file is automatically generated by <PERSON><PERSON>, do not modify it manually.
// Modify your Keystone config when you want to change this.

datasource postgresql {
  url               = env("DATABASE_URL")
  shadowDatabaseUrl = env("SHADOW_DATABASE_URL")
  provider          = "postgresql"
}

generator client {
  provider = "prisma-client-js"
}

model User {
  id               String            @id @default(cuid())
  name             String            @default("")
  email            String            @unique @default("")
  password         String
  role             Role?             @relation("User_role", fields: [roleId], references: [id])
  roleId           String?           @map("role")
  shops            Shop[]            @relation("Shop_user")
  channels         Channel[]         @relation("Channel_user")
  orders           Order[]           @relation("Order_user")
  lineItems        LineItem[]        @relation("LineItem_user")
  cartItems        CartItem[]        @relation("CartItem_user")
  shopItems        ShopItem[]        @relation("ShopItem_user")
  channelItems     ChannelItem[]     @relation("ChannelItem_user")
  matches          Match[]           @relation("Match_user")
  links            Link[]            @relation("Link_user")
  trackingDetails  TrackingDetail[]  @relation("TrackingDetail_user")
  shopPlatforms    ShopPlatform[]    @relation("ShopPlatform_user")
  channelPlatforms ChannelPlatform[] @relation("ChannelPlatform_user")
  apiKeys          ApiKey[]          @relation("ApiKey_user")
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @default(now())

  @@index([roleId])
}

model Role {
  id                     String  @id @default(cuid())
  name                   String  @default("")
  canSeeOtherUsers       Boolean @default(false)
  canEditOtherUsers      Boolean @default(false)
  canManageUsers         Boolean @default(false)
  canManageRoles         Boolean @default(false)
  canAccessDashboard     Boolean @default(false)
  canSeeOtherShops       Boolean @default(false)
  canManageShops         Boolean @default(false)
  canCreateShops         Boolean @default(false)
  canSeeOtherChannels    Boolean @default(false)
  canManageChannels      Boolean @default(false)
  canCreateChannels      Boolean @default(false)
  canSeeOtherOrders      Boolean @default(false)
  canManageOrders        Boolean @default(false)
  canProcessOrders       Boolean @default(false)
  canSeeOtherMatches     Boolean @default(false)
  canManageMatches       Boolean @default(false)
  canCreateMatches       Boolean @default(false)
  canSeeOtherLinks       Boolean @default(false)
  canManageLinks         Boolean @default(false)
  canCreateLinks         Boolean @default(false)
  canManagePlatforms     Boolean @default(false)
  canViewPlatformMetrics Boolean @default(false)
  canManageApiKeys       Boolean @default(false)
  canCreateApiKeys       Boolean @default(false)
  canAccessAnalytics     Boolean @default(false)
  canExportData          Boolean @default(false)
  canManageWebhooks      Boolean @default(false)
  assignedTo             User[]  @relation("User_role")
}

model ApiKey {
  id        String   @id @default(cuid())
  user      User?    @relation("ApiKey_user", fields: [userId], references: [id])
  userId    String?  @map("user")
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@index([userId])
}

model ShopPlatform {
  id                             String   @id @default(cuid())
  name                           String   @default("")
  appKey                         String   @default("")
  appSecret                      String   @default("")
  orderLinkFunction              String   @default("")
  updateProductFunction          String   @default("")
  getWebhooksFunction            String   @default("")
  deleteWebhookFunction          String   @default("")
  createWebhookFunction          String   @default("")
  searchProductsFunction         String   @default("")
  getProductFunction             String   @default("")
  searchOrdersFunction           String   @default("")
  addTrackingFunction            String   @default("")
  addCartToPlatformOrderFunction String   @default("")
  cancelOrderWebhookHandler      String   @default("")
  createOrderWebhookHandler      String   @default("")
  oAuthFunction                  String   @default("")
  oAuthCallbackFunction          String   @default("")
  shops                          Shop[]   @relation("Shop_platform")
  user                           User?    @relation("ShopPlatform_user", fields: [userId], references: [id])
  userId                         String?  @map("user")
  createdAt                      DateTime @default(now())
  updatedAt                      DateTime @default(now())

  @@index([userId])
}

model ChannelPlatform {
  id                           String    @id @default(cuid())
  name                         String    @default("")
  appKey                       String    @default("")
  appSecret                    String    @default("")
  createPurchaseFunction       String    @default("")
  searchProductsFunction       String    @default("")
  getProductFunction           String    @default("")
  getWebhooksFunction          String    @default("")
  deleteWebhookFunction        String    @default("")
  createWebhookFunction        String    @default("")
  cancelPurchaseWebhookHandler String    @default("")
  createTrackingWebhookHandler String    @default("")
  oAuthFunction                String    @default("")
  oAuthCallbackFunction        String    @default("")
  channels                     Channel[] @relation("Channel_platform")
  user                         User?     @relation("ChannelPlatform_user", fields: [userId], references: [id])
  userId                       String?   @map("user")
  createdAt                    DateTime  @default(now())
  updatedAt                    DateTime  @default(now())

  @@index([userId])
}

model Shop {
  id          String        @id @default(cuid())
  name        String        @default("")
  domain      String        @default("")
  accessToken String        @default("")
  linkMode    String?       @default("sequential")
  metadata    Json?         @default("{}")
  platform    ShopPlatform? @relation("Shop_platform", fields: [platformId], references: [id])
  platformId  String?       @map("platform")
  user        User?         @relation("Shop_user", fields: [userId], references: [id])
  userId      String?       @map("user")
  links       Link[]        @relation("Link_shop")
  orders      Order[]       @relation("Order_shop")
  shopItems   ShopItem[]    @relation("ShopItem_shop")
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @default(now())

  @@index([platformId])
  @@index([userId])
}

model Channel {
  id           String           @id @default(cuid())
  name         String           @default("")
  domain       String           @default("")
  accessToken  String           @default("")
  metadata     Json?            @default("{}")
  platform     ChannelPlatform? @relation("Channel_platform", fields: [platformId], references: [id])
  platformId   String?          @map("platform")
  user         User?            @relation("Channel_user", fields: [userId], references: [id])
  userId       String?          @map("user")
  links        Link[]           @relation("Link_channel")
  channelItems ChannelItem[]    @relation("ChannelItem_channel")
  cartItems    CartItem[]       @relation("CartItem_channel")
  createdAt    DateTime         @default(now())
  updatedAt    DateTime         @default(now())

  @@index([platformId])
  @@index([userId])
}

model Order {
  id             String     @id @default(cuid())
  orderId        String     @unique @default("")
  orderName      String     @default("")
  email          String     @default("")
  first_name     String     @default("")
  last_name      String     @default("")
  streetAddress1 String     @default("")
  streetAddress2 String     @default("")
  city           String     @default("")
  state          String     @default("")
  zip            String     @default("")
  country        String     @default("")
  phone          String     @default("")
  currency       String     @default("")
  totalPrice     Float?
  subTotalPrice  Float?
  totalDiscounts Float?
  totalTax       Float?
  linkOrder      Boolean    @default(true)
  matchOrder     Boolean    @default(true)
  processOrder   Boolean    @default(true)
  status         String     @default("PENDING")
  error          String     @default("")
  orderMetadata  Json?
  shop           Shop?      @relation("Order_shop", fields: [shopId], references: [id])
  shopId         String?    @map("shop")
  lineItems      LineItem[] @relation("LineItem_order")
  cartItems      CartItem[] @relation("CartItem_order")
  user           User?      @relation("Order_user", fields: [userId], references: [id])
  userId         String?    @map("user")
  createdAt      DateTime   @default(now())
  updatedAt      DateTime   @default(now())

  @@index([shopId])
  @@index([userId])
}

model LineItem {
  id         String   @id @default(cuid())
  name       String   @default("")
  image      String   @default("")
  price      Float?
  quantity   Int?
  productId  String   @default("")
  variantId  String   @default("")
  sku        String   @default("")
  lineItemId String   @default("")
  order      Order?   @relation("LineItem_order", fields: [orderId], references: [id])
  orderId    String?  @map("order")
  user       User?    @relation("LineItem_user", fields: [userId], references: [id])
  userId     String?  @map("user")
  createdAt  DateTime @default(now())
  updatedAt  DateTime @default(now())

  @@index([orderId])
  @@index([userId])
}

model CartItem {
  id              String           @id @default(cuid())
  name            String           @default("")
  image           String           @default("")
  price           String           @default("")
  quantity        Int?
  productId       String           @default("")
  variantId       String           @default("")
  sku             String           @default("")
  lineItemId      String           @default("")
  url             String           @default("")
  error           String           @default("")
  purchaseId      String           @default("")
  status          String           @default("PENDING")
  order           Order?           @relation("CartItem_order", fields: [orderId], references: [id])
  orderId         String?          @map("order")
  channel         Channel?         @relation("CartItem_channel", fields: [channelId], references: [id])
  channelId       String?          @map("channel")
  trackingDetails TrackingDetail[] @relation("CartItem_trackingDetails")
  user            User?            @relation("CartItem_user", fields: [userId], references: [id])
  userId          String?          @map("user")
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @default(now())

  @@index([orderId])
  @@index([channelId])
  @@index([userId])
}

model ShopItem {
  id         String   @id @default(cuid())
  quantity   Int?
  productId  String   @default("")
  variantId  String   @default("")
  lineItemId String   @default("")
  matches    Match[]  @relation("Match_input")
  shop       Shop?    @relation("ShopItem_shop", fields: [shopId], references: [id])
  shopId     String?  @map("shop")
  user       User?    @relation("ShopItem_user", fields: [userId], references: [id])
  userId     String?  @map("user")
  createdAt  DateTime @default(now())
  updatedAt  DateTime @default(now())

  @@unique([quantity, productId, variantId, shopId, userId])
  @@index([shopId])
  @@index([userId])
}

model ChannelItem {
  id         String   @id @default(cuid())
  quantity   Int?
  productId  String   @default("")
  variantId  String   @default("")
  lineItemId String   @default("")
  price      String   @default("")
  matches    Match[]  @relation("ChannelItem_matches")
  channel    Channel? @relation("ChannelItem_channel", fields: [channelId], references: [id])
  channelId  String?  @map("channel")
  user       User?    @relation("ChannelItem_user", fields: [userId], references: [id])
  userId     String?  @map("user")
  createdAt  DateTime @default(now())
  updatedAt  DateTime @default(now())

  @@unique([quantity, productId, variantId, channelId, userId])
  @@index([channelId])
  @@index([userId])
}

model Match {
  id        String        @id @default(cuid())
  input     ShopItem[]    @relation("Match_input")
  output    ChannelItem[] @relation("ChannelItem_matches")
  user      User?         @relation("Match_user", fields: [userId], references: [id])
  userId    String?       @map("user")
  createdAt DateTime      @default(now())
  updatedAt DateTime      @default(now())

  @@index([userId])
}

model Link {
  id          String   @id @default(cuid())
  rank        Int?     @default(1)
  filters     Json?    @default("[]")
  customWhere Json?    @default("{}")
  shop        Shop?    @relation("Link_shop", fields: [shopId], references: [id])
  shopId      String?  @map("shop")
  channel     Channel? @relation("Link_channel", fields: [channelId], references: [id])
  channelId   String?  @map("channel")
  user        User?    @relation("Link_user", fields: [userId], references: [id])
  userId      String?  @map("user")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @default(now())

  @@index([shopId])
  @@index([channelId])
  @@index([userId])
}

model TrackingDetail {
  id              String     @id @default(cuid())
  trackingCompany String     @default("")
  trackingNumber  String     @default("")
  purchaseId      String     @default("")
  cartItems       CartItem[] @relation("CartItem_trackingDetails")
  user            User?      @relation("TrackingDetail_user", fields: [userId], references: [id])
  userId          String?    @map("user")
  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @default(now())

  @@index([userId])
}
