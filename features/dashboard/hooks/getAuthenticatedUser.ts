import { cache } from 'react';
import { getAuthenticatedUser as getAuthenticatedUserAction } from '@/features/dashboard/actions';
import type { User } from '@/features/dashboard/components/DashboardUI'; 

export const getAuthenticatedUser = cache(async (): Promise<User | null> => {
  const response = await getAuthenticatedUserAction();
  
  if (!response.success || !response.data?.authenticatedItem) {
    return null;
  }
  
  return response.data.authenticatedItem;
}); 