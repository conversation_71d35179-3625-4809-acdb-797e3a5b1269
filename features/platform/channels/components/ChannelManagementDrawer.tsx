"use client";

import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON><PERSON><PERSON>,
} from "@/components/ui/drawer";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Ticket, SquareStack, Webhook, Tv, ArrowRightLeft } from "lucide-react";
import Link from "next/link";
import { Webhooks } from "./Webhooks";
import MatchList from "../../shops/components/MatchList";
import { SearchOrders } from "./SearchOrders";
import { AdvancedLinks } from "./AdvancedLinks";
import type { Channel } from "../lib/types";

interface ChannelManagementDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  channel: Channel | undefined;
}

export function ChannelManagementDrawer({
  open,
  onO<PERSON>Chang<PERSON>,
  channel,
}: ChannelManagementDrawerProps) {
  if (!channel) return null;


  return (
    <Drawer open={open} onOpenChange={onOpenChange}>
      <DrawerContent className="overflow-x-hidden sm:max-w-lg">
        <DrawerHeader className="-px-6 w-full">
          <DrawerTitle className="flex w-full items-start justify-between">
            <div className="flex flex-col gap-1">
              <span>{channel.name}</span>
              <span className="text-sm text-muted-foreground font-normal">
                {new Date(channel.createdAt).toLocaleDateString("en-US", {
                  year: "numeric",
                  month: "short",
                  day: "numeric",
                })}
              </span>
            </div>
            {channel.platform && (
              <Badge variant="secondary" className="text-xs bg-zinc-100 text-zinc-700 dark:bg-zinc-800 dark:text-zinc-300 border border-zinc-200 dark:border-zinc-700 rounded-full px-3 py-1">
                {channel.platform.name}
              </Badge>
            )}
          </DrawerTitle>
        </DrawerHeader>
        <DrawerBody className="-mx-6 overflow-y-scroll">
          <Tabs defaultValue="orders">
            <TabsList className="justify-start w-full h-auto gap-2 rounded-none bg-transparent px-6 py-0 border-b border-border">
              <TabsTrigger 
                value="orders" 
                className="text-muted-foreground relative pb-3 pt-2 px-3 data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:text-foreground hover:text-foreground transition-colors after:absolute after:bottom-0 after:left-0 after:right-0 after:h-[2px] data-[state=active]:after:bg-foreground rounded-none"
              >
                <Ticket className="-ms-0.5 me-1.5 opacity-60" size={16} />
                <span className="hidden sm:inline">Orders</span>
              </TabsTrigger>
              <TabsTrigger 
                value="matches" 
                className="text-muted-foreground relative pb-3 pt-2 px-3 data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:text-foreground hover:text-foreground transition-colors after:absolute after:bottom-0 after:left-0 after:right-0 after:h-[2px] data-[state=active]:after:bg-foreground rounded-none"
              >
                <SquareStack className="-ms-0.5 me-1.5 opacity-60" size={16} />
                <span className="hidden sm:inline">Matches</span>
              </TabsTrigger>
              <TabsTrigger 
                value="links" 
                className="text-muted-foreground relative pb-3 pt-2 px-3 data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:text-foreground hover:text-foreground transition-colors after:absolute after:bottom-0 after:left-0 after:right-0 after:h-[2px] data-[state=active]:after:bg-foreground rounded-none"
              >
                <ArrowRightLeft className="-ms-0.5 me-1.5 opacity-60" size={16} />
                <span className="hidden sm:inline">Links</span>
              </TabsTrigger>
              <TabsTrigger 
                value="webhooks" 
                className="text-muted-foreground relative pb-3 pt-2 px-3 data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:text-foreground hover:text-foreground transition-colors after:absolute after:bottom-0 after:left-0 after:right-0 after:h-[2px] data-[state=active]:after:bg-foreground rounded-none"
              >
                <Webhook className="-ms-0.5 me-1.5 opacity-60" size={16} />
                <span className="hidden sm:inline">Webhooks</span>
              </TabsTrigger>
            </TabsList>
            <TabsContent value="orders" className="space-y-6 px-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium text-sm">Recent Orders</h4>
                  <Link
                    href={`/dashboard/platform/orders?channel=${channel.id}`}
                    className="text-xs text-muted-foreground hover:text-blue-600"
                  >
                    View all →
                  </Link>
                </div>
                <SearchOrders
                  channelId={channel.id}
                  pageSize={3}
                />
              </div>
            </TabsContent>
            <TabsContent value="matches" className="space-y-6 px-6">
              <MatchList
                channelId={channel.id}
                onMatchAction={() => {}}
                showCreate={false}
              />
            </TabsContent>
            <TabsContent value="links" className="space-y-6 px-6">
              <AdvancedLinks channelId={channel.id} />
            </TabsContent>
            <TabsContent value="webhooks" className="space-y-6 px-6">
              <div className="space-y-4">
                <div className="text-sm text-muted-foreground mb-4">
                  Create platform webhooks to keep Openship in sync
                </div>
                <Webhooks channelId={channel.id} />
              </div>
            </TabsContent>
          </Tabs>
        </DrawerBody>
        <DrawerFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}