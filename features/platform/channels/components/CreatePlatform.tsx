"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  <PERSON><PERSON>T<PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { createChannelPlatform } from "../actions";
import { toast } from "sonner";

interface CreatePlatformProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  trigger?: React.ReactNode;
}

export function CreatePlatform({ open, onOpenChange, trigger }: CreatePlatformProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    appKey: "",
    appSecret: "",
    oAuthFunction: "",
    oAuthCallbackFunction: "",
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await createChannelPlatform(formData);
      
      if (response.success) {
        toast.success("Channel platform created successfully");
        setFormData({
          name: "",
          appKey: "",
          appSecret: "",
          oAuthFunction: "",
          oAuthCallbackFunction: "",
        });
        onOpenChange(false);
        // Refresh the page to show the new platform
        window.location.reload();
      } else {
        toast.error(response.error || "Failed to create channel platform");
      }
    } catch (error) {
      console.error("Error creating channel platform:", error);
      toast.error("An unexpected error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create Channel Platform</DialogTitle>
          <DialogDescription>
            Add a new channel platform to connect your sales channels.
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Platform Name</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              placeholder="e.g., Amazon, eBay, Etsy"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="appKey">App Key</Label>
            <Input
              id="appKey"
              value={formData.appKey}
              onChange={(e) => handleInputChange("appKey", e.target.value)}
              placeholder="Application key or client ID"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="appSecret">App Secret</Label>
            <Input
              id="appSecret"
              type="password"
              value={formData.appSecret}
              onChange={(e) => handleInputChange("appSecret", e.target.value)}
              placeholder="Application secret or client secret"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="oAuthFunction">OAuth Function</Label>
            <Textarea
              id="oAuthFunction"
              value={formData.oAuthFunction}
              onChange={(e) => handleInputChange("oAuthFunction", e.target.value)}
              placeholder="OAuth authorization function code"
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="oAuthCallbackFunction">OAuth Callback Function</Label>
            <Textarea
              id="oAuthCallbackFunction"
              value={formData.oAuthCallbackFunction}
              onChange={(e) => handleInputChange("oAuthCallbackFunction", e.target.value)}
              placeholder="OAuth callback function code"
              rows={3}
            />
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Creating..." : "Create Platform"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
