"use client";

import React, { useState, useEffect, useTransition } from "react";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  ArrowRight,
  Search,
  CircleAlert,
  ChevronDown,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { searchChannelOrders } from "../actions/search-orders";

// Simple currency formatter
const formatCurrency = (amount: number, currency: string = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
  }).format(amount / 100); // Assuming amount is in cents
};

interface Order {
  id: string;
  orderId: string;
  orderName: string;
  email: string;
  firstName: string;
  lastName: string;
  streetAddress1: string;
  streetAddress2?: string;
  city: string;
  state: string;
  zip: string;
  orderError?: string;
  currency: string;
  totalPrice: number;
  subTotalPrice: number;
  totalDiscount: number;
  totalTax: number;
  status: string;
  createdAt: string;
  cartItemsCount: number;
  lineItemsCount: number;
  cartItems: Array<{
    id: string;
    name: string;
    image?: string;
    price: number;
    quantity: number;
    productId: string;
    variantId?: string;
    purchaseId?: string;
    url?: string;
    error?: string;
    channel: {
      id: string;
      name: string;
    };
  }>;
  shop: {
    id: string;
    name: string;
    domain: string;
  };
}

export function SearchOrders({
  channelId,
  searchEntry = "",
  pageSize = 10,
  onOrderSelect,
  hideSearchBar = false,
}: {
  channelId: string;
  searchEntry?: string;
  pageSize?: number;
  onOrderSelect?: (order: Order) => void;
  hideSearchBar?: boolean;
}) {
  const [searchInput, setSearchInput] = useState(searchEntry);
  const [activeSearch, setActiveSearch] = useState(searchEntry);
  const [skip, setSkip] = useState(0);
  const [orders, setOrders] = useState<Order[]>([]);
  const [isPending, startTransition] = useTransition();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedOrders, setExpandedOrders] = useState<Set<string>>(new Set());

  const loadOrders = async (search: string, skipCount: number) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await searchChannelOrders(channelId, search, pageSize, skipCount);
      
      if (response.orders) {
        setOrders(response.orders);
      } else {
        setError("Failed to load orders");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load orders");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadOrders(activeSearch, skip);
  }, [channelId, activeSearch, skip]);

  const handleSearch = () => {
    setSkip(0);
    setActiveSearch(searchInput);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  const handleNextPage = () => {
    startTransition(() => {
      setSkip(skip + pageSize);
    });
  };

  const handlePreviousPage = () => {
    startTransition(() => {
      setSkip(Math.max(0, skip - pageSize));
    });
  };

  const toggleOrderExpanded = (orderId: string) => {
    const newExpanded = new Set(expandedOrders);
    if (newExpanded.has(orderId)) {
      newExpanded.delete(orderId);
    } else {
      newExpanded.add(orderId);
    }
    setExpandedOrders(newExpanded);
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "pending":
        return "amber";
      case "inprocess":
        return "blue";
      case "awaiting":
        return "purple";
      case "backordered":
        return "orange";
      case "cancelled":
        return "red";
      case "complete":
        return "emerald";
      default:
        return "gray";
    }
  };

  if (error) {
    return (
      <div className="flex items-center gap-2 p-4 text-red-600">
        <CircleAlert className="h-4 w-4" />
        <span>{error}</span>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4">
      {!hideSearchBar && (
        <div className="flex items-center gap-3">
          <div className="flex gap-1">
            <Button
              variant="outline"
              size="icon"
              onClick={handlePreviousPage}
              disabled={skip === 0 || isPending}
              className="h-8 w-8"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>

            <Button
              variant="outline"
              size="icon"
              onClick={handleNextPage}
              disabled={orders.length < pageSize || isPending}
              className="h-8 w-8"
            >
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
          <div className="relative flex-grow">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              className="w-full rounded-md bg-muted/40 pl-9"
              placeholder="Search orders..."
              type="text"
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
              onKeyPress={handleKeyPress}
            />
          </div>
          <Button onClick={handleSearch} disabled={isPending}>
            Search
          </Button>
        </div>
      )}

      <div className="space-y-3">
        {isLoading ? (
          Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="space-y-2">
              <Skeleton className="h-16 w-full" />
            </div>
          ))
        ) : orders.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            {activeSearch ? "No orders found matching your search." : "No orders found for this channel."}
          </div>
        ) : (
          orders.map((order) => (
            <Collapsible
              key={order.id}
              open={expandedOrders.has(order.id)}
              onOpenChange={() => toggleOrderExpanded(order.id)}
            >
              <div className="border rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div>
                      <div className="font-medium">{order.orderName}</div>
                      <div className="text-sm text-muted-foreground">
                        {order.firstName} {order.lastName}
                      </div>
                    </div>
                    <Badge 
                      variant="outline"
                      className={cn("text-xs", getStatusBadgeColor(order.status))}
                    >
                      {order.status}
                    </Badge>
                  </div>

                  <div className="flex items-center gap-2">
                    <div className="text-right">
                      <div className="font-medium">
                        {formatCurrency(order.totalPrice, order.currency)}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {order.cartItemsCount} items from {order.shop.name}
                      </div>
                    </div>
                    <CollapsibleTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <ChevronDown className={cn(
                          "h-4 w-4 transition-transform",
                          expandedOrders.has(order.id) && "transform rotate-180"
                        )} />
                      </Button>
                    </CollapsibleTrigger>
                  </div>
                </div>

                <CollapsibleContent className="space-y-3">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <div className="font-medium">Customer Details</div>
                      <div className="text-muted-foreground">
                        <div>{order.email}</div>
                        <div>{order.streetAddress1}</div>
                        {order.streetAddress2 && <div>{order.streetAddress2}</div>}
                        <div>{order.city}, {order.state} {order.zip}</div>
                      </div>
                    </div>
                    <div>
                      <div className="font-medium">Order Details</div>
                      <div className="text-muted-foreground">
                        <div>Order ID: {order.orderId}</div>
                        <div>Shop: {order.shop.name}</div>
                        <div>Created: {new Date(order.createdAt).toLocaleDateString()}</div>
                      </div>
                    </div>
                  </div>

                  {order.cartItems.length > 0 && (
                    <div>
                      <div className="font-medium mb-2">Cart Items for this Channel</div>
                      <div className="space-y-2">
                        {order.cartItems.map((item) => (
                          <div key={item.id} className="flex items-center gap-3 p-2 bg-muted/50 rounded">
                            {item.image && (
                              <img 
                                src={item.image} 
                                alt={item.name}
                                className="w-10 h-10 object-cover rounded"
                              />
                            )}
                            <div className="flex-grow">
                              <div className="font-medium text-sm">{item.name}</div>
                              <div className="text-xs text-muted-foreground">
                                Qty: {item.quantity} × {formatCurrency(item.price, order.currency)}
                              </div>
                            </div>
                            {item.error && (
                              <Badge variant="destructive" className="text-xs">
                                Error
                              </Badge>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {onOrderSelect && (
                    <Button 
                      onClick={() => onOrderSelect(order)}
                      variant="outline"
                      size="sm"
                    >
                      Select Order
                    </Button>
                  )}
                </CollapsibleContent>
              </div>
            </Collapsible>
          ))
        )}
      </div>

      {orders.length === pageSize && (
        <div className="text-center text-sm text-muted-foreground">
          Showing {skip + 1}-{skip + orders.length} orders
        </div>
      )}
    </div>
  );
}