"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ead<PERSON>, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { ShoppingBag, Package, Store, Globe } from "lucide-react";

interface PlatformTemplate {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  fields: {
    name: string;
    appKey: boolean;
    appSecret: boolean;
    oAuth: boolean;
  };
}

const shopTemplates: PlatformTemplate[] = [
  {
    id: "shopify",
    name: "Shopify",
    description: "Connect your Shopify store",
    icon: ShoppingBag,
    fields: {
      name: "Shopify",
      appKey: true,
      appSecret: true,
      oAuth: true,
    },
  },
  {
    id: "bigcommerce",
    name: "BigCommerce",
    description: "Connect your BigCommerce store",
    icon: Package,
    fields: {
      name: "BigCommerce",
      appKey: true,
      appSecret: true,
      oAuth: true,
    },
  },
  {
    id: "woocommerce",
    name: "WooCommerce",
    description: "Connect your WooCommerce store",
    icon: Store,
    fields: {
      name: "WooCommerce",
      appKey: true,
      appSecret: true,
      oAuth: false,
    },
  },
];

const channelTemplates: PlatformTemplate[] = [
  {
    id: "shopify",
    name: "Shopify",
    description: "Sell on Shopify",
    icon: ShoppingBag,
    fields: {
      name: "Shopify",
      appKey: true,
      appSecret: true,
      oAuth: true,
    },
  },
  {
    id: "amazon",
    name: "Amazon",
    description: "Sell on Amazon",
    icon: Globe,
    fields: {
      name: "Amazon",
      appKey: true,
      appSecret: true,
      oAuth: false,
    },
  },
];

interface PlatformDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  platformType: "shop" | "channel";
  onSubmit: (data: any) => Promise<void>;
  platform?: any; // For editing existing platform
}

export function PlatformDialog({
  open,
  onOpenChange,
  platformType,
  onSubmit,
  platform,
}: PlatformDialogProps) {
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    name: platform?.name || "",
    appKey: platform?.appKey || "",
    appSecret: platform?.appSecret || "",
    // Add more fields as needed based on your platform model
  });

  const templates = platformType === "shop" ? shopTemplates : channelTemplates;
  const isEditing = !!platform;

  const handleTemplateSelect = (templateId: string) => {
    const template = templates.find((t) => t.id === templateId);
    if (template) {
      setSelectedTemplate(templateId);
      setFormData({
        ...formData,
        name: template.fields.name,
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      await onSubmit(formData);
      onOpenChange(false);
      // Reset form
      setFormData({
        name: "",
        appKey: "",
        appSecret: "",
      });
      setSelectedTemplate(null);
    } catch (error) {
      console.error("Error submitting platform:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? "Edit" : "Create"} {platformType === "shop" ? "Shop" : "Channel"} Platform
          </DialogTitle>
        </DialogHeader>

        {!isEditing ? (
          <Tabs defaultValue="templates" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="templates">Templates</TabsTrigger>
              <TabsTrigger value="custom">Custom</TabsTrigger>
            </TabsList>

            <TabsContent value="templates" className="space-y-4">
              <div className="grid gap-4">
                {templates.map((template) => {
                  const Icon = template.icon;
                  return (
                    <Card
                      key={template.id}
                      className={cn(
                        "cursor-pointer transition-all",
                        selectedTemplate === template.id && "ring-2 ring-primary"
                      )}
                      onClick={() => handleTemplateSelect(template.id)}
                    >
                      <CardHeader>
                        <div className="flex items-center gap-3">
                          <Icon className="h-8 w-8" />
                          <div>
                            <CardTitle className="text-lg">{template.name}</CardTitle>
                            <CardDescription>{template.description}</CardDescription>
                          </div>
                        </div>
                      </CardHeader>
                    </Card>
                  );
                })}
              </div>

              {selectedTemplate && (
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Platform Name</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      placeholder="Enter platform name"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="appKey">App Key / Client ID</Label>
                    <Input
                      id="appKey"
                      value={formData.appKey}
                      onChange={(e) => setFormData({ ...formData, appKey: e.target.value })}
                      placeholder="Enter app key or client ID"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="appSecret">App Secret / Client Secret</Label>
                    <Input
                      id="appSecret"
                      type="password"
                      value={formData.appSecret}
                      onChange={(e) => setFormData({ ...formData, appSecret: e.target.value })}
                      placeholder="Enter app secret or client secret"
                      required
                    />
                  </div>

                  <div className="flex justify-end gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => onOpenChange(false)}
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isSubmitting}>
                      {isSubmitting ? "Creating..." : "Create Platform"}
                    </Button>
                  </div>
                </form>
              )}
            </TabsContent>

            <TabsContent value="custom" className="space-y-4">
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="custom-name">Platform Name</Label>
                  <Input
                    id="custom-name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="Enter platform name"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="custom-appKey">App Key / Client ID</Label>
                  <Input
                    id="custom-appKey"
                    value={formData.appKey}
                    onChange={(e) => setFormData({ ...formData, appKey: e.target.value })}
                    placeholder="Enter app key or client ID"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="custom-appSecret">App Secret / Client Secret</Label>
                  <Input
                    id="custom-appSecret"
                    type="password"
                    value={formData.appSecret}
                    onChange={(e) => setFormData({ ...formData, appSecret: e.target.value })}
                    placeholder="Enter app secret or client secret"
                  />
                </div>

                <p className="text-sm text-muted-foreground">
                  For custom platforms, you'll need to implement the integration functions manually.
                </p>

                <div className="flex justify-end gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => onOpenChange(false)}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? "Creating..." : "Create Platform"}
                  </Button>
                </div>
              </form>
            </TabsContent>
          </Tabs>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="edit-name">Platform Name</Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Enter platform name"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-appKey">App Key / Client ID</Label>
              <Input
                id="edit-appKey"
                value={formData.appKey}
                onChange={(e) => setFormData({ ...formData, appKey: e.target.value })}
                placeholder="Enter app key or client ID"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-appSecret">App Secret / Client Secret</Label>
              <Input
                id="edit-appSecret"
                type="password"
                value={formData.appSecret}
                onChange={(e) => setFormData({ ...formData, appSecret: e.target.value })}
                placeholder="Enter app secret or client secret"
              />
            </div>

            <div className="flex justify-end gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "Saving..." : "Save Changes"}
              </Button>
            </div>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
}

function cn(...classes: (string | undefined | null | false)[]) {
  return classes.filter(Boolean).join(" ");
}