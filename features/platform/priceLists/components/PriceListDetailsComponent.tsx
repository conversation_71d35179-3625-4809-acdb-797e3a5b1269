"use client";

import React, { useState } from "react";
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from "@/components/ui/accordion";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { MoreVertical, Receipt, Info, Calendar, Users, Package, DollarSign, TrendingDown, Tag } from "lucide-react";
import Link from "next/link";
import { EditItemDrawer } from "@/features/dashboard/components/EditItemDrawer";
import { PriceList } from "../lib/types";

const statusColors = {
  active: "emerald",
  draft: "zinc",
} as const;

const typeColors = {
  sale: "blue",
  override: "purple",
} as const;

interface PriceListDetailsComponentProps {
  priceList: PriceList & { 
    moneyAmountsCount?: number;
    customerGroupsCount?: number;
    moneyAmounts?: Array<{
      id: string;
      amount: number;
      compareAmount?: number;
      minQuantity?: number;
      maxQuantity?: number;
      currency?: {
        id: string;
        code: string;
        symbol?: string;
      };
      productVariant?: {
        id: string;
        title: string;
        sku?: string;
        product?: {
          id: string;
          title: string;
          thumbnail?: string;
        };
      };
      region?: {
        id: string;
        name: string;
      };
    }>;
    customerGroups?: Array<{
      id: string;
      name: string;
      metadata?: any;
    }>;
  };
}

export function PriceListDetailsComponent({
  priceList,
}: PriceListDetailsComponentProps) {
  const [isEditDrawerOpen, setIsEditDrawerOpen] = useState(false);

  const moneyAmountsCount = priceList.moneyAmountsCount || 0;
  const customerGroupsCount = priceList.customerGroupsCount || 0;

  const formatDate = (dateStr: string | null | undefined) => {
    if (!dateStr) return "Not set";
    return new Date(dateStr).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const formatCurrency = (amount: number, currencyCode?: string, symbol?: string) => {
    if (symbol) {
      return `${symbol}${(amount / 100).toFixed(2)}`;
    }
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currencyCode || 'USD',
    }).format(amount / 100);
  };

  const getDiscountPercentage = (amount: number, compareAmount?: number) => {
    if (!compareAmount || compareAmount <= amount) return null;
    return Math.round(((compareAmount - amount) / compareAmount) * 100);
  };

  return (
    <>
      <Accordion type="single" collapsible className="w-full">
        <AccordionItem value={priceList.id} className="border-0">
          <div className="px-4 md:px-6 py-3 md:py-4 flex justify-between w-full border-b relative min-h-[80px]">
            <div className="flex items-start gap-4">
              {/* Price List Info */}
              <div className="flex flex-col items-start text-left gap-2 sm:gap-1.5">
                <div className="flex flex-wrap items-center gap-2">
                  <Link
                    href={`/dashboard/platform/price-lists/${priceList.id}`}
                    className="font-medium text-base hover:text-blue-600 dark:hover:text-blue-400"
                  >
                    {priceList.name}
                  </Link>
                  <span>‧</span>

                  <span className="text-sm font-medium">
                    <span className="text-muted-foreground/75">
                      {new Date(priceList.createdAt).toLocaleDateString("en-US", {
                        year: "numeric",
                        month: "long",
                        day: "numeric",
                      })}
                    </span>
                  </span>
                </div>

                <span className="text-muted-foreground text-xs">
                  {priceList.description}
                </span>
                
                <div className="flex flex-wrap items-center gap-1.5 text-xs text-muted-foreground">
                  <div className="flex items-center gap-1.5">
                    <Receipt className="size-3" />
                    <span className="font-medium">{moneyAmountsCount}</span>
                    <span>price{moneyAmountsCount !== 1 ? "s" : ""}</span>
                  </div>
                  {customerGroupsCount > 0 && (
                    <>
                      <span>‧</span>
                      <div className="flex items-center gap-1.5">
                        <Users className="size-3" />
                        <span className="font-medium">{customerGroupsCount}</span>
                        <span>group{customerGroupsCount !== 1 ? "s" : ""}</span>
                      </div>
                    </>
                  )}
                  {(priceList.startsAt || priceList.endsAt) && (
                    <>
                      <span>‧</span>
                      <div className="flex items-center gap-1.5">
                        <Calendar className="size-3" />
                        <span>
                          {priceList.startsAt && `Starts: ${formatDate(priceList.startsAt)}`}
                          {priceList.startsAt && priceList.endsAt && " - "}
                          {priceList.endsAt && `Ends: ${formatDate(priceList.endsAt)}`}
                        </span>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>

            <div className="flex flex-col justify-between h-full">
              <div className="flex items-center gap-2">
                <Badge
                  color={statusColors[priceList.status]}
                  className="text-[.6rem] sm:text-[.7rem] py-0 px-2 sm:px-3 tracking-wide font-medium rounded-md border h-6"
                >
                  {priceList.status.toUpperCase()}
                </Badge>
                <Badge
                  color={typeColors[priceList.type]}
                  className="text-[.6rem] sm:text-[.7rem] py-0 px-2 sm:px-3 tracking-wide font-medium rounded-md border h-6"
                >
                  {priceList.type.toUpperCase()}
                </Badge>
                {/* Single buttons container */}
                <div className="absolute bottom-3 right-5 sm:static flex items-center gap-2">
                  <Button
                    variant="secondary"
                    size="icon"
                    className="border [&_svg]:size-3 h-6 w-6"
                    onClick={() => setIsEditDrawerOpen(true)}
                  >
                    <MoreVertical className="stroke-muted-foreground" />
                  </Button>
                  
                  {/* Price List Details Popover */}
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="secondary"
                        size="icon"
                        className="border [&_svg]:size-3 h-6 w-6"
                      >
                        <Info className="stroke-muted-foreground" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-80" align="end">
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <h4 className="font-medium text-sm">Price List Details</h4>
                          <div className="grid grid-cols-2 gap-3 text-sm">
                            <div>
                              <span className="text-muted-foreground">Type:</span>
                              <div className="font-medium capitalize">
                                {priceList.type}
                              </div>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Status:</span>
                              <div className="font-medium capitalize">
                                {priceList.status}
                              </div>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Prices:</span>
                              <div className="font-medium">
                                {moneyAmountsCount}
                              </div>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Groups:</span>
                              <div className="font-medium">
                                {customerGroupsCount}
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Schedule Section */}
                        {(priceList.startsAt || priceList.endsAt) && (
                          <div className="space-y-2 pt-2 border-t">
                            <h4 className="font-medium text-sm">Schedule</h4>
                            <div className="space-y-1 text-sm">
                              {priceList.startsAt && (
                                <div>
                                  <span className="text-muted-foreground">Starts:</span>
                                  <span className="ml-2 font-medium">
                                    {formatDate(priceList.startsAt)}
                                  </span>
                                </div>
                              )}
                              {priceList.endsAt && (
                                <div>
                                  <span className="text-muted-foreground">Ends:</span>
                                  <span className="ml-2 font-medium">
                                    {formatDate(priceList.endsAt)}
                                  </span>
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </PopoverContent>
                  </Popover>

                  <Button
                    variant="secondary"
                    size="icon"
                    className="border [&_svg]:size-3 h-6 w-6"
                    asChild
                  >
                    <AccordionTrigger className="py-0" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
          <AccordionContent className="pb-0">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 p-4 md:p-6">
              {/* Price List Information */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Receipt className="h-4 w-4" />
                    Price List Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3 text-sm">
                  <div>
                    <span className="text-muted-foreground">Description:</span>
                    <div className="mt-1">{priceList.description}</div>
                  </div>
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <span className="text-muted-foreground">Type:</span>
                      <div className="mt-1 capitalize">
                        <Badge color={typeColors[priceList.type]} variant="outline">
                          {priceList.type}
                        </Badge>
                      </div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Status:</span>
                      <div className="mt-1 capitalize">
                        <Badge color={statusColors[priceList.status]} variant="outline">
                          {priceList.status}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  {(priceList.startsAt || priceList.endsAt) && (
                    <div>
                      <span className="text-muted-foreground">Schedule:</span>
                      <div className="mt-1 text-sm">
                        {priceList.startsAt && (
                          <div>Starts: {formatDate(priceList.startsAt)}</div>
                        )}
                        {priceList.endsAt && (
                          <div>Ends: {formatDate(priceList.endsAt)}</div>
                        )}
                      </div>
                    </div>
                  )}
                  {priceList.customerGroups && priceList.customerGroups.length > 0 && (
                    <div>
                      <span className="text-muted-foreground">Customer Groups:</span>
                      <div className="mt-1 flex flex-wrap gap-1">
                        {priceList.customerGroups.map((group) => (
                          <Badge key={group.id} variant="secondary" className="text-xs">
                            {group.name}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Product Prices */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Package className="h-4 w-4" />
                    Product Prices
                    <Badge variant="secondary" className="ml-auto">
                      {moneyAmountsCount}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-[300px]">
                    {priceList.moneyAmounts && priceList.moneyAmounts.length > 0 ? (
                      <div className="space-y-3">
                        {priceList.moneyAmounts.map((moneyAmount: any) => {
                          const discountPercent = getDiscountPercentage(moneyAmount.amount, moneyAmount.compareAmount);
                          return (
                            <div
                              key={moneyAmount.id}
                              className="p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
                            >
                              <div className="flex items-start justify-between">
                                <div className="flex-1">
                                  {moneyAmount.productVariant && (
                                    <div className="flex items-center gap-2 mb-2">
                                      <Package className="h-3 w-3" />
                                      <Link 
                                        href={`/dashboard/platform/products/${moneyAmount.productVariant.product?.id}`}
                                        className="text-sm font-medium text-blue-600 hover:text-blue-700 dark:text-blue-400"
                                      >
                                        {moneyAmount.productVariant.product?.title}
                                      </Link>
                                    </div>
                                  )}
                                  {moneyAmount.productVariant?.title && (
                                    <div className="text-xs text-muted-foreground mb-1">
                                      Variant: {moneyAmount.productVariant.title}
                                    </div>
                                  )}
                                  {moneyAmount.productVariant?.sku && (
                                    <div className="text-xs text-muted-foreground mb-2">
                                      SKU: {moneyAmount.productVariant.sku}
                                    </div>
                                  )}
                                  {(moneyAmount.minQuantity || moneyAmount.maxQuantity) && (
                                    <div className="text-xs text-muted-foreground mb-2">
                                      Quantity: {moneyAmount.minQuantity || 1} - {moneyAmount.maxQuantity || '∞'}
                                    </div>
                                  )}
                                  {moneyAmount.region && (
                                    <div className="text-xs text-muted-foreground">
                                      Region: {moneyAmount.region.name}
                                    </div>
                                  )}
                                </div>
                                <div className="text-right">
                                  <div className="flex items-center gap-2">
                                    <DollarSign className="h-3 w-3" />
                                    <span className="font-medium text-sm">
                                      {formatCurrency(
                                        moneyAmount.amount, 
                                        moneyAmount.currency?.code,
                                        moneyAmount.currency?.symbol
                                      )}
                                    </span>
                                  </div>
                                  {moneyAmount.compareAmount && (
                                    <div className="flex items-center gap-2 mt-1">
                                      <span className="text-xs text-muted-foreground line-through">
                                        {formatCurrency(
                                          moneyAmount.compareAmount,
                                          moneyAmount.currency?.code,
                                          moneyAmount.currency?.symbol
                                        )}
                                      </span>
                                      {discountPercent && (
                                        <Badge color="green" className="text-xs">
                                          <TrendingDown className="h-3 w-3 mr-1" />
                                          {discountPercent}% off
                                        </Badge>
                                      )}
                                    </div>
                                  )}
                                  <div className="text-xs text-muted-foreground mt-1">
                                    {moneyAmount.currency?.code || 'USD'}
                                  </div>
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    ) : (
                      <div className="text-center text-sm text-muted-foreground py-8">
                        No prices configured yet
                      </div>
                    )}
                  </ScrollArea>
                </CardContent>
              </Card>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      <EditItemDrawer
        listKey="price-lists"
        itemId={priceList.id}
        open={isEditDrawerOpen}
        onClose={() => setIsEditDrawerOpen(false)}
      />
    </>
  );
}